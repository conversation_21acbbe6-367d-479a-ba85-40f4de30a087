// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"esop/internal/handler"
	"esop/internal/mqtt"
	"esop/internal/repository"
	"esop/internal/server"
	"esop/internal/service"
	"esop/pkg/app"
	"esop/pkg/jwt"
	"esop/pkg/server/http"
	"github.com/google/wire"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *zap.Logger) (*app.App, func(), error) {
	jwtJWT := jwt.NewJwt(viperViper)
	handlerHandler := handler.NewHandler(logger)
	mqttServer, err := mqtt.NewMqttServer(viperViper)
	if err != nil {
		return nil, nil, err
	}
	internalClient := repository.NewInternalMqttClient(mqttServer)
	serviceService := service.NewService(logger, jwtJWT, internalClient)
	db := repository.NewDb(viperViper, logger)
	repositoryRepository := repository.NewRepository(viperViper, logger, db, internalClient)
	templateRepository := repository.NewTemplateRepository(repositoryRepository)
	materialGroupRepository := repository.NewMaterialGroupRepository(repositoryRepository)
	sourceMaterialRepository := repository.NewSourceMaterialRepository(repositoryRepository)
	templateService := service.NewTemplateService(serviceService, templateRepository, materialGroupRepository, sourceMaterialRepository)
	templateHandler := handler.NewTemplateHandler(handlerHandler, templateService)
	equipmentRepository := repository.NewEquipmentRepository(repositoryRepository)
	equipmentService := service.NewEquipmentService(serviceService, equipmentRepository, internalClient)
	equipmentHandler := handler.NewEquipmentHandler(handlerHandler, equipmentService)
	adminRepository := repository.NewAdminRepository(repositoryRepository)
	adminService := service.NewAdminService(serviceService, adminRepository)
	adminHandler := handler.NewAdminHandler(handlerHandler, adminService)
	sourceMaterialService := service.NewSourceMaterialService(serviceService, sourceMaterialRepository, equipmentRepository)
	sourceMaterialHandler := handler.NewSourceMaterialHandler(handlerHandler, sourceMaterialService)
	resourcePackRepository := repository.NewResourcePackRepository(repositoryRepository)
	resourcePackService := service.NewResourcePackService(serviceService, resourcePackRepository, equipmentRepository)
	resourcePackHandler := handler.NewResourcePackHandler(handlerHandler, resourcePackService)
	operationLogRepository := repository.NewOperationLogRepository(repositoryRepository)
	operationLogService := service.NewOperationLogService(serviceService, operationLogRepository)
	operationLogHandler := handler.NewOperationLogHandler(handlerHandler, operationLogService)
	materialGroupService := service.NewMaterialGroupService(serviceService, materialGroupRepository, sourceMaterialRepository)
	materialGroupHandler := handler.NewMaterialGroupHandler(handlerHandler, materialGroupService)
	licenseRepository := repository.NewLicenseRepository(repositoryRepository)
	licenseService := service.NewLicenseService(serviceService, licenseRepository, equipmentRepository)
	licenseHandler := handler.NewLicenseHandler(handlerHandler, licenseService)
	httpServer := server.NewServerHTTP(jwtJWT, logger, viperViper, templateHandler, equipmentHandler, adminHandler, sourceMaterialHandler, resourcePackHandler, operationLogHandler, materialGroupHandler, licenseHandler)
	task := server.NewTask(logger, equipmentService)
	mqttServerAdapter := mqtt.NewMqttServerAdapter(mqttServer)
	appApp := newApp(httpServer, task, mqttServerAdapter)
	return appApp, func() {
	}, nil
}

// wire.go:

var ServerSet = wire.NewSet(server.NewServerHTTP, server.NewTask, mqtt.NewMqttServer, mqtt.NewMqttServerAdapter)

var RepositorySet = wire.NewSet(repository.NewDb, repository.NewRepository, repository.NewTemplateRepository, repository.NewEquipmentRepository, repository.NewAdminRepository, repository.NewSourceMaterialRepository, repository.NewResourcePackRepository, repository.NewOperationLogRepository, repository.NewMaterialGroupRepository, repository.NewInternalMqttClient, repository.NewLicenseRepository)

var ServiceSet = wire.NewSet(service.NewService, service.NewTemplateService, service.NewEquipmentService, service.NewAdminService, service.NewSourceMaterialService, service.NewResourcePackService, service.NewOperationLogService, service.NewMaterialGroupService, service.NewLicenseService)

var HandlerSet = wire.NewSet(handler.NewHandler, handler.NewTemplateHandler, handler.NewEquipmentHandler, handler.NewAdminHandler, handler.NewSourceMaterialHandler, handler.NewResourcePackHandler, handler.NewOperationLogHandler, handler.NewMaterialGroupHandler, handler.NewLicenseHandler)

func newApp(httpServer *http.Server, task *server.Task, mqttAdapter *mqtt.MqttServerAdapter) *app.App {
	return app.NewApp(app.WithServer(httpServer, task, mqttAdapter), app.WithName("system-server"))
}
