//go:build wireinject
// +build wireinject

package wire

import (
	"esop/internal/mqtt"
	"esop/internal/repository"
	"esop/internal/server"
	"esop/internal/service"
	"esop/pkg/app"
	"esop/pkg/jwt"

	"github.com/google/wire"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

var RepositorySet = wire.NewSet(
	repository.NewDb,
	repository.NewRepository,
	repository.NewEquipmentRepository,
)

var ServiceSet = wire.NewSet(
	service.NewService,
	service.NewEquipmentService,
)

var serverSet = wire.NewSet(
	server.NewTask,
)

// NewNilMqttClient provides a nil-safe mqtt.InternalClient for the task application,
// as it does not require a direct connection to the MQTT server broker.
func NewNilMqttClient() mqtt.InternalClient {
	return &nilMqttClient{}
}

// nilMqttClient is a no-op implementation of InternalClient for task applications
type nilMqttClient struct{}

func (n *nilMqttClient) Publish(topic string, qos byte, retained bool, payload interface{}) error {
	return nil // no-op
}

func (n *nilMqttClient) Disconnect(quiesce uint) {
	// no-op
}

func (n *nilMqttClient) IsConnected() bool {
	return false
}

func (n *nilMqttClient) GetConnectedClients() []string {
	return []string{} // return empty slice for task applications
}

// build App
func newApp(
	task *server.Task,
) *app.App {
	return app.NewApp(
		app.WithServer(task),
		app.WithName("esop-task"),
	)
}

func NewWire(*viper.Viper, *zap.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		RepositorySet,
		ServiceSet,
		serverSet,
		jwt.NewJwt,
		NewNilMqttClient,
		newApp,
	))
}
