# MQTT 设备在线状态检测改进

## 问题背景

原有的设备在线状态检测机制存在以下问题：

1. **依赖设备主动上报**：设备需要定期调用 `/equipment/life` 接口更新 `updated_at` 时间戳
2. **网络不稳定时误判**：在网络不稳定或设备异常情况下，设备可能无法及时调用接口，导致误判为离线
3. **检查频率高**：每7秒检查一次，对数据库造成不必要的压力
4. **延迟较大**：设备离线后最多需要90秒才能被检测到

## 解决方案

### 核心改进

使用 MQTT 连接状态作为设备在线判断的主要依据：

1. **实时性更好**：MQTT 连接断开时立即更新状态
2. **更可靠**：直接反映设备的网络连接状态
3. **减少误判**：避免因网络抖动导致的误判
4. **降低系统负载**：减少定时检查频率

### 技术实现

#### 1. 扩展 MQTT 内部客户端接口

在 `internal/mqtt/internal_client.go` 中添加 `GetConnectedClients()` 方法：

```go
type InternalClient interface {
    Publish(topic string, qos byte, retained bool, payload interface{}) error
    Disconnect(quiesce uint)
    IsConnected() bool
    GetConnectedClients() []string  // 新增方法
}
```

#### 2. 实现获取连接客户端功能

```go
func (c *internalMqttClient) GetConnectedClients() []string {
    if c.server == nil || c.server.Clients == nil {
        return []string{}
    }
    
    allClients := c.server.Clients.GetAll()
    connectedClients := make([]string, 0, len(allClients))
    
    for clientID, client := range allClients {
        if client != nil && !client.Closed() {
            connectedClients = append(connectedClients, clientID)
        }
    }
    
    return connectedClients
}
```

#### 3. 更新设备状态检查逻辑

在 `internal/service/equipment.go` 中实现基于 MQTT 连接状态的检查：

```go
func (s *equipmentService) CheckEquipmentStatusByMQTT() error {
    // 获取所有设备
    allEquipments, err := s.equipmentRepository.ScanEquipmentTime()
    if err != nil {
        return err
    }

    // 获取当前 MQTT 连接的客户端
    connectedClients := s.getMQTTConnectedClients()
    
    // 创建连接状态映射
    connectedMap := make(map[string]bool)
    for _, clientID := range connectedClients {
        connectedMap[clientID] = true
    }

    // 更新设备状态
    for _, equipment := range allEquipments {
        var newStatus int
        if connectedMap[equipment.MacAddress] {
            newStatus = 1 // 在线
        } else {
            newStatus = 2 // 离线
        }

        // 只有状态发生变化时才更新数据库
        if equipment.Status != newStatus {
            if err := s.equipmentRepository.SetEquipmentStatusByMacAddress(equipment.MacAddress, newStatus); err != nil {
                s.logger.Error("failed to update equipment status", 
                    zap.String("mac_address", equipment.MacAddress), 
                    zap.Int("new_status", newStatus), 
                    zap.Error(err))
                continue
            }
            s.logger.Info("equipment status updated", 
                zap.String("mac_address", equipment.MacAddress), 
                zap.Int("old_status", equipment.Status), 
                zap.Int("new_status", newStatus))
        }
    }
    return nil
}
```

#### 4. 优化定时任务频率

将定时检查频率从7秒调整为30秒：

```go
// 使用 MQTT 连接状态检查设备在线状态，降低检查频率到30秒
_, err = t.scheduler.NewJob(gocron.DurationJob(30*time.Second), gocron.NewTask(func() {
    t.log.Info("Checking equipment status via MQTT connections")
    if checkErr := t.equipment.CheckEquipmentStatus(); checkErr != nil {
        t.log.Error("Failed to check equipment status", zap.Error(checkErr))
        return
    }
}))
```

#### 5. 兼容性处理

为 task 应用提供 nil 安全的 MQTT 客户端实现：

```go
type nilMqttClient struct{}

func (n *nilMqttClient) GetConnectedClients() []string {
    return []string{} // 返回空切片
}
```

## 优势对比

| 特性 | 原有机制 | 新机制 |
|------|----------|--------|
| 检测方式 | 设备主动上报 | MQTT 连接状态 |
| 实时性 | 最多90秒延迟 | 实时检测 |
| 可靠性 | 依赖网络稳定性 | 基于连接状态 |
| 系统负载 | 每7秒检查一次 | 每30秒检查一次 |
| 误判率 | 网络抖动时较高 | 显著降低 |

## 向后兼容性

1. **保留原有接口**：`/equipment/life` 接口仍然可用
2. **MQTT Hook 机制**：原有的连接/断开 Hook 继续工作
3. **数据库结构**：无需修改现有数据库结构
4. **API 兼容**：前端无需修改

## 测试验证

已添加单元测试验证核心功能：

- `TestGetMQTTConnectedClients`：测试获取连接客户端功能
- `TestGetMQTTConnectedClientsWithNilClient`：测试 nil 客户端的处理

## 部署建议

1. **逐步迁移**：可以先在测试环境验证效果
2. **监控观察**：部署后观察设备状态变化的准确性
3. **日志记录**：关注状态变化的日志，确保逻辑正确
4. **性能监控**：观察系统负载的改善情况

## 总结

这个改进显著提升了设备在线状态检测的可靠性和实时性，同时降低了系统负载。通过使用 MQTT 连接状态作为判断依据，避免了网络不稳定情况下的误判问题，为系统的稳定运行提供了更好的保障。
