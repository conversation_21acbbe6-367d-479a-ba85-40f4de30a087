package server

import (
	"esop/internal/handler"
	"esop/internal/middleware"
	"esop/pkg/helper/resp"
	"esop/pkg/jwt"
	"esop/pkg/server/http"
	"esop/web"

	"github.com/gin-contrib/static"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

func NewServerHTTP(
	jwt *jwt.JWT,
	logger *zap.Logger,
	conf *viper.Viper,
	templateHandler *handler.TemplateHandler,
	equipmentHandler *handler.EquipmentHandler,
	adminHandler *handler.AdminHandler,
	sourceMaterialHandler *handler.SourceMaterialHandler,
	resourcePackHandler *handler.ResourcePackHandler,
	operationLogHandler *handler.OperationLogHandler,
	materialGroupHandler *handler.MaterialGroupHandler,
	licenseHandler *handler.LicenseHandler,
) *http.Server {
	gin.SetMode(gin.ReleaseMode)
	engine := gin.Default()
	r := http.NewServer(
		engine,
		logger,
		http.WithServerHost(conf.GetString("http.host")),
		http.WithServerPort(conf.GetInt("http.port")),
	)
	engine.Use(middleware.Logger(logger))
	engine.Use(
		middleware.CORSMiddleware(),
	)

	// 嵌入式静态资源服务
	fs, err := static.EmbedFolder(web.Assets(), "dist")
	if err != nil {
		// 处理错误，例如记录日志或者终止程序
		panic(err)
	}

	engine.Use(static.Serve("/dashboard", fs))

	//engine.NoRoute(func(c *gin.Context) {
	//	indexPageData, err := web.Assets().ReadFile("dist/index.html")
	//	if err != nil {
	//		c.String(nethttp.StatusNotFound, "404 page not found")
	//		return
	//	}
	//	c.Data(nethttp.StatusOK, "text/html; charset=utf-8", indexPageData)
	//})

	// 设置静态文件服务
	engine.Static("/assets/media", "./assets/media")
	engine.Static("/assets/zippack", "./assets/zippack")
	engine.Static("/assets/ota_update", "./assets/ota_update")
	engine.Static("/assets/screenshots", "./assets/screenshots")
	engine.GET("/", func(ctx *gin.Context) {
		resp.HandleSuccess(ctx, map[string]interface{}{
			"say": "Hi Nunu!",
		})
	})

	// 设置前端静态资源

	/*
		=================--test
		|| AUTH-Router ||
		=================
	*/
	authorized := engine.Group("/")
	authorized.Use(middleware.StrictAuth(jwt, logger))
	// noAuthorized := engine.Group("/")
	{
		admin := authorized.Group("/admin")
		{

			// 设备中心
			equipment := admin.Group("equipment")
			{
				// equipment.POST("addEquipment", equipmentHandler.AddEquipment)    //新增设备
				equipment.GET("getList", equipmentHandler.GetList)              // 设备列表
				equipment.GET("/groupNameList", equipmentHandler.GroupNameList) // 新增设备
				equipment.POST("/addGroup", equipmentHandler.AddGroup)
				equipment.DELETE("/deleteGroup/:groud_id", equipmentHandler.DeleteGroup) // 删除分组
				equipment.GET("/getTreeList", equipmentHandler.GetTreeList)              // 设备列表树
				equipment.DELETE("delete/:id", equipmentHandler.DeleteEquipment)         // 删除设备
				equipment.PUT("/editEquipment", equipmentHandler.EditEquipment)          // 修改设备
				equipment.GET("getGroupList", equipmentHandler.GetGroupList)             // 查看列表数据
				equipment.POST("/sendCommand", equipmentHandler.SendCommand)             // 发送指令
				equipment.GET("/getMaterial/:id", equipmentHandler.GetMaterial)
				equipment.POST("/associateMaterial", equipmentHandler.AssociateMaterial)
				equipment.POST("/disassociateMaterial", equipmentHandler.DisassociateMaterial)
				equipment.POST("/pushMaterial", equipmentHandler.PushMaterial)
				equipment.POST("/batchPushMaterial", equipmentHandler.BatchPushMaterial)
				equipment.GET("/getLatestScreenshot", equipmentHandler.GetLatestScreenshot) // 获取设备最新截图
				equipment.GET("/getScreenshotByTaskId", equipmentHandler.GetScreenshotByTaskId)
				equipment.DELETE("/screenshot", equipmentHandler.DeleteScreenshot)
				equipment.GET("/log", equipmentHandler.GetEquipmentLog)
				equipment.DELETE("/log", equipmentHandler.ClearEquipmentLog)
				equipment.POST("/latest_report", equipmentHandler.GetLatestDeviceReport)    // 获取设备最新上报信息
				equipment.POST("/current_open_files", equipmentHandler.GetCurrentOpenFiles) // 获取设备当前打开文件

				// 定时开关机
				equipment.POST("/schedules", equipmentHandler.SaveEquipmentPowerSchedules)
				equipment.GET("/schedules/:id", equipmentHandler.GetEquipmentPowerSchedules)
				equipment.POST("/batchDeleteFiles", equipmentHandler.BatchDeleteFiles)
			}
			// 模板中心
			template := admin.Group("template")
			{
				template.POST("/addTemplate", templateHandler.AddTemplate)    // 新增模板
				template.GET("getList", templateHandler.GetList)              // 模板列表
				template.DELETE("delete/:id", templateHandler.DeleteTemplate) // 删除模板
				template.GET("getDetail", templateHandler.GetDetail)          // 查看单条数据
				template.PUT("edit/:id", templateHandler.EditTemplateInfo)    // 编辑
				template.POST("savePack", templateHandler.AddPack)            // 打包项目
				template.GET("/material_tree", templateHandler.GetMaterialTree)

			}
			// 素材管理
			sourceMaterial := admin.Group("sourcematerial")
			{

				sourceMaterial.GET("getList", sourceMaterialHandler.GetList)                                  // 素材列表
				sourceMaterial.GET("getDetail", sourceMaterialHandler.GetDetail)                              // 查看单条数据
				sourceMaterial.DELETE("delete", sourceMaterialHandler.DeleteSourceMaterial)                   // 删除账号
				sourceMaterial.POST("addSourceMaterial", sourceMaterialHandler.AddSourceMaterial)             // 新增素材
				sourceMaterial.POST("upload", sourceMaterialHandler.Upload)                                   // 上传素材
				sourceMaterial.POST("upload/equipment", sourceMaterialHandler.UploadToEquipment)              // 上传素材到设备
				sourceMaterial.POST("batchUpload", sourceMaterialHandler.BatchUpload)                         // 批量上传
				sourceMaterial.POST("batchUploadNew", sourceMaterialHandler.BatchUploadNew)                   // 新的批量上传接口
				sourceMaterial.POST("batchUploadNewWithGroup", sourceMaterialHandler.BatchUploadNewWithGroup) // 带分组的批量上传接口
				sourceMaterial.PUT("editSourceMaterial/:id", sourceMaterialHandler.EditSourceMaterialInfo)    // 编辑
				sourceMaterial.PUT("updateMaterialsGroup", sourceMaterialHandler.UpdateMaterialsGroup)        // 批量更新素材分组

			}
			// 素材分组管理
			materialGroup := admin.Group("materialgroup")
			{
				materialGroup.GET("getList", materialGroupHandler.GetList)                     // 分组列表
				materialGroup.GET("getAllGroups", materialGroupHandler.GetAllGroups)           // 获取所有分组
				materialGroup.GET("getDetail", materialGroupHandler.GetDetail)                 // 查看单条数据
				materialGroup.POST("create", materialGroupHandler.Create)                      // 新增分组
				materialGroup.PUT("edit/:id", materialGroupHandler.Update)                     // 编辑分组
				materialGroup.DELETE("delete/:id", materialGroupHandler.Delete)                // 删除分组
				materialGroup.POST("moveMaterials", materialGroupHandler.MoveMaterialsToGroup) // 移动素材到分组
			}
			// 操作日志
			operationlog := admin.Group("operationlog")
			{
				operationlog.GET("getList", operationLogHandler.GetList) // 操作日志列表
			}

			// 授权管理
			license := admin.Group("license")
			{
				license.GET("/server/machine_code/export", licenseHandler.ExportServerMachineCode)
				license.POST("/server/license/import", licenseHandler.ImportServerLicense)
				license.GET("/server/status", licenseHandler.GetServerStatus)
				license.POST("/server/license/generate", licenseHandler.GenerateServerLicense)
				license.GET("/terminal/status", licenseHandler.GetTerminalStatus)
				license.GET("/terminal/machine_codes/export", licenseHandler.ExportTerminalMachineCodes)
				license.POST("/terminal/registration_codes/import", licenseHandler.ImportTerminalRegistrationCodes)
			}
			// 资源包
			resourcePack := admin.Group("resourcepack")
			{

				resourcePack.GET("getList", resourcePackHandler.GetList)                  // 资源包列表
				resourcePack.DELETE("delete/:id", resourcePackHandler.DeleteResourcePack) // 删除资源包
				resourcePack.POST("/pub", resourcePackHandler.Pub)                        // 发送资源包
				resourcePack.POST("/pubByPackName", resourcePackHandler.PubByPackName)    // 按包名发送资源包
				// admin.POST("updateUsing", adminHandler.EditUsing)    //启用/停用

			}
			// 账号管理
			admin := admin.Group("admin")
			{
				// admin.POST("login", adminHandler.Login)                 //登录
				admin.POST("addAdmin", adminHandler.AddAdmin)        // 新增账号
				admin.PUT("editAdmin/:id", adminHandler.EditAdmin)   // 编辑账号
				admin.GET("getList", adminHandler.GetList)           // 账号列表
				admin.DELETE("delete/:id", adminHandler.DeleteAdmin) // 删除账号
				admin.POST("updateUsing", adminHandler.EditUsing)    // 启用/停用
				// admin.GET("pub", adminHandler.Pub)    //测试MQTT推送
				admin.GET("sub", adminHandler.Sub) // 测试MQTT拉取

			}

		}

	}

	v1 := engine.Group("v1") //
	{
		v1.POST("login", adminHandler.Login)
		v1.POST("equipment/addEquipment", equipmentHandler.AddEquipment)               // 新增设备
		v1.PUT("equipment/lifeEquipment", equipmentHandler.LifeEquipment)              // 设备保活
		v1.GET("/equipment/equipmentDetail:macAddress", equipmentHandler.GetEquipment) // 设备详情
		v1.POST("equipment/equipmentLog/:macAddress", equipmentHandler.EquipmentLog)   // 设备状态上报
		v1.PUT("equipment/updateEquipment", equipmentHandler.EditEquipment)            // 设备修改信息
		v1.GET("equipment/checkEquipment", equipmentHandler.CheckEquipment)            // 检测设备是否已激活
		v1.GET("equipment/ota", equipmentHandler.GetOTA)                               // 设备上报接口
		v1.GET("/equipment/getMaterial/:id", equipmentHandler.GetMaterial)             // 获取素材
		v1.POST("equipment/reported", equipmentHandler.EquipmentReported)              // 设备单个上报
		v1.POST("equipment/reported/batch", equipmentHandler.EquipmentReportedBatch)   // 设备批量上报
		v1.POST("equipment/screenshot", equipmentHandler.UploadScreenshot)             // 上传设备截屏

	}

	return r
}
