package server

import (
	"context"
	"fmt"
	"time"

	"esop/internal/service"

	"github.com/go-co-op/gocron/v2"
	"go.uber.org/zap"
)

type Task struct {
	log       *zap.Logger
	scheduler gocron.Scheduler
	equipment service.EquipmentService
}

func NewTask(log *zap.Logger, equipment service.EquipmentService) *Task {
	return &Task{
		log:       log,
		equipment: equipment,
	}
}

func (t *Task) Start(ctx context.Context) error {
	var err error
	t.scheduler, err = gocron.NewScheduler()
	if err != nil {
		fmt.Println(err, "scheduler error")
	}
	t.log.Info("Task start...")
	// 使用 MQTT 连接状态检查设备在线状态，降低检查频率到30秒
	_, err = t.scheduler.NewJob(gocron.DurationJob(30*time.Second), gocron.NewTask(func() {
		t.log.Info("Checking equipment status via MQTT connections")
		if checkErr := t.equipment.CheckEquipmentStatus(); checkErr != nil {
			t.log.Error("Failed to check equipment status", zap.Error(checkErr))
			return
		}
	}))
	if err != nil {
		t.log.Error("Failed to create equipment status check job", zap.Error(err))
		return err
	}
	t.scheduler.Start() // 开启任务
	return nil
}

func (t *Task) Stop(ctx context.Context) error {
	if err := t.scheduler.Shutdown(); err != nil {
		t.log.Info("Task stop...Error")
		return err
	}
	t.log.Info("Task stop...")
	return nil
}
