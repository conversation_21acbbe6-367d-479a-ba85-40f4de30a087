package handler

import (
	"net/http"
	"strconv"

	v1 "esop/api/v1"
	"esop/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ResourcePackHandler struct {
	*Handler
	resourcePackService service.ResourcePackService
}

func NewResourcePackHandler(handler *Handler, resourcePackService service.ResourcePackService) *ResourcePackHandler {
	return &ResourcePackHandler{
		Handler:             handler,
		resourcePackService: resourcePackService,
	}
}

func (h *ResourcePackHandler) GetResourcePack(ctx *gin.Context) {
}

func (h *ResourcePackHandler) GetList(ctx *gin.Context) {
	var req v1.ResourcePackListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, err)
		return
	}
	total, equipmentList, err := h.resourcePackService.GetResourcePackList(ctx, req)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	v1.HandleSuccess(ctx, nil, v1.PagingResponse{
		PagingData: *v1.Paging(total, req.PagingRequest),
		Data:       equipmentList,
	})
}

func (h *ResourcePackHandler) DeleteResourcePack(ctx *gin.Context) {
	id, _ := strconv.Atoi(ctx.Param("id")) // 从路由参数中获取id
	err := h.resourcePackService.DeleteResourcePack(ctx, id)
	if err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrBadRequest, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)
}

func (h *ResourcePackHandler) Pub(ctx *gin.Context) {
	var req v1.PubRequest
	if err := ctx.BindJSON(&req); err != nil {

		h.logger.Error("req", zap.Error(err))
		v1.HandleError(ctx, 400, err, nil)
		return
	}

	err := h.resourcePackService.Pub(ctx, req)
	if err != nil {
		v1.HandleError(ctx, http.StatusOK, v1.ErrBadRequest, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)
}

// PubByPackName 按包名发送资源包
func (h *ResourcePackHandler) PubByPackName(ctx *gin.Context) {
	var req v1.PubByPackNameRequest
	if err := ctx.BindJSON(&req); err != nil {
		h.logger.Error("PubByPackName req", zap.Error(err))
		v1.HandleError(ctx, 400, err, nil)
		return
	}

	err := h.resourcePackService.PubByPackName(ctx, req)
	if err != nil {
		v1.HandleError(ctx, http.StatusOK, err, err)
		return
	}
	v1.HandleSuccess(ctx, nil, nil)
}
