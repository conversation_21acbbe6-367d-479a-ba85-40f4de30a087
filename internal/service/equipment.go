package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"hash"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	v1 "esop/api/v1"
	"esop/internal/model"
	"esop/internal/mqtt"
	"esop/internal/repository"
	"esop/pkg/helper/decode"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type EquipmentService interface {
	// GetEquipment(id int64) (*model.Equipment, error)
	GetEquipmentList(ctx context.Context, req v1.EquipmentListRequest) (int64, []*v1.EquipmentData, error)
	GetEquipmentGroupList(ctx context.Context, req v1.EquipmentListGroupRequest) (int64, []*v1.EquipmentGroupData, error)
	AddEquipment(ctx *gin.Context, v *v1.SaveEquipmentListRequest) error

	DeleteEquipment(ctx *gin.Context, id int) error
	CheckEquipment(ctx *gin.Context, macAddress string) error
	LifeEquipment(ctx *gin.Context, id string) (v1.EquipmentData, error)
	GetEquipmentTreeList(ctx *gin.Context, req v1.EquipmentListRequest) ([]v1.EquipmentTreeData, error)
	CheckEquipmentStatus() error
	EditEquipment(ctx *gin.Context, req v1.SaveEquipmentListRequest) error
	GroupNameList(ctx *gin.Context) ([]v1.EquipmentGroupData, error)
	DeleteGroup(ctx *gin.Context, groud_id int) error
	AddGroup(ctx *gin.Context, req v1.GroupRequest) error
	GetEquipment(ctx *gin.Context, address string) (v1.EquipmentTree, error)

	// ProcessEquipmentReport 处理单个设备上报数据
	ProcessEquipmentReport(ctx context.Context, report v1.ReportedData) error

	// ProcessEquipmentReportBatch 处理批量设备上报数据
	ProcessEquipmentReportBatch(ctx context.Context, reports []v1.ReportedData) error

	// CleanOldEquipmentReports 清理指定天数前的设备上报数据
	CleanOldEquipmentReports(days int) error

	SendCommand(ctx context.Context, req v1.SendCommandRequest) (string, error)
	GetMaterialByEquipmentId(ctx context.Context, id int) ([]v1.SourceMaterialData, error)
	AssociateMaterial(ctx context.Context, req v1.AssociateMaterialRequest) error
	DisassociateMaterial(ctx context.Context, req v1.AssociateMaterialRequest) error
	PushMaterial(ctx context.Context, req v1.PushMaterialRequest) error
	BatchPushMaterial(ctx context.Context, req v1.BatchPushMaterialRequest) error
	UploadScreenshot(ctx context.Context, file *multipart.FileHeader, groupName, equipmentAliasName, taskId string) (string, error)
	GetLatestScreenshotByEquipment(ctx context.Context, groupName, equipmentAliasName string) (*v1.LatestScreenshotResponse, error)
	GetScreenshotByTaskId(ctx context.Context, taskId string) (*v1.LatestScreenshotResponse, error)
	DeleteEquipmentScreenshot(ctx context.Context, id int64) error
	GetEquipmentLog(ctx context.Context, req v1.EquipmentLogRequest) (int64, []*model.EquipmentReport, error)
	GetLatestDeviceReport(ctx context.Context, req v1.LatestDeviceReportRequest) (map[string]*model.EquipmentReport, error)
	GetCurrentOpenFiles(ctx context.Context, req v1.GetCurrentOpenFilesRequest) (*v1.GetCurrentOpenFilesResponse, error)
	ClearEquipmentLog(ctx context.Context, req v1.ClearEquipmentLogRequest) error
	SaveEquipmentPowerSchedules(ctx context.Context, req v1.SaveSchedulePowerRequest) error
	GetEquipmentPowerSchedules(ctx context.Context, equipmentId int) ([]*model.EquipmentPowerSchedule, error)
	BatchDeleteFiles(ctx context.Context, req v1.BatchDeleteFilesRequest) error
}

func NewEquipmentService(service *Service, equipmentRepository repository.EquipmentRepository, mqttClient mqtt.InternalClient) EquipmentService {
	return &equipmentService{
		Service:             service,
		equipmentRepository: equipmentRepository,
		mqttClient:          mqttClient,
	}
}

type equipmentService struct {
	*Service
	equipmentRepository repository.EquipmentRepository
	mqttClient          mqtt.InternalClient
}

//	func (s *equipmentService) GetEquipment(ctx id int64) (*model.Equipment, error) {
//		return s.equipmentRepository.FirstById(ctx,id)
//	}
func (s *equipmentService) GetEquipmentList(ctx context.Context, req v1.EquipmentListRequest) (int64, []*v1.EquipmentData, error) {
	total, info, err := s.equipmentRepository.GetEquipmentList(ctx, req)
	if err != nil {
		return 0, nil, err
	}

	return total, info, nil
}

func (s *equipmentService) GetEquipmentGroupList(ctx context.Context, req v1.EquipmentListGroupRequest) (int64, []*v1.EquipmentGroupData, error) {
	total, info, err := s.equipmentRepository.GetEquipmentGroupList(ctx, req)
	if err != nil {
		return 0, nil, err
	}

	return total, info, nil
}

func (s *equipmentService) AddEquipment(ctx *gin.Context, v *v1.SaveEquipmentListRequest) error {
	if v.RegistrationCode == "" {
		return s.equipmentRepository.Create(ctx, v)
	}
	code, err := decode.CheckTerminalDecryptAES(v.RegistrationCode)
	if err != nil {
		return err
	}

	fmt.Println(err, code, "cc121212121", v.RegistrationCode)
	if code != fmt.Sprintf("%s#%s", v.MacAddress, v.SerialNumber) {
		return v1.NewError(9999, "注册码错误")
	}
	return s.equipmentRepository.Create(ctx, v)
}

func (s *equipmentService) DeleteEquipment(ctx *gin.Context, id int) error {
	return s.equipmentRepository.Delete(ctx, id)
}

func (s *equipmentService) CheckEquipment(ctx *gin.Context, macAddress string) error {
	return s.equipmentRepository.CheckEquipment(ctx, macAddress)
}

func (s *equipmentService) LifeEquipment(ctx *gin.Context, id string) (v1.EquipmentData, error) {
	return s.equipmentRepository.LifeEquipment(ctx, id)
}

func (s *equipmentService) GetEquipmentTreeList(ctx *gin.Context, req v1.EquipmentListRequest) ([]v1.EquipmentTreeData, error) {
	var TreeData []v1.EquipmentTreeData
	var onlineNum int
	group, err := s.equipmentRepository.GetEquipmentGroupData()
	if err != nil {
		return nil, err
	}
	for _, value := range group {
		var TeData []v1.EquipmentTree
		onlineNum = 0
		eData, err := s.equipmentRepository.GetEquipmentData(ctx, value.ID)
		if err != nil {
			return nil, err
		}
		if len(eData) == 0 {
			eData = nil
		} else {
			for _, eValue := range eData {
				TeData = append(TeData, v1.EquipmentTree{
					ID:            eValue.ID,
					IsClient:      true,
					IsOnline:      eValue.Status == 1,
					GroupName:     value.Name,
					MacAddress:    eValue.MacAddress,
					AliasName:     eValue.AliasName,
					UpdatedAt:     eValue.UpdatedAt,
					Label:         eValue.AliasName,
					IpAddr:        eValue.IpAddr,
					ScreenWidth:   eValue.ScreenWidth,
					ScreenHeight:  eValue.ScreenHeight,
					System:        eValue.System,
					SerialNumber:  eValue.SerialNumber,
					Model:         eValue.Model,
					Manufacturer:  eValue.Manufacturer,
					Hardware:      eValue.Hardware,
					Product:       eValue.Product,
					SystemVersion: eValue.SystemVersion,
				})
				if eValue.Status == 1 {
					onlineNum += 1
				}
			}

			// 按照AliasName进行自然语言排序
			sort.Slice(TeData, func(i, j int) bool {
				return naturalLess(TeData[i].AliasName, TeData[j].AliasName)
			})
		}

		TreeData = append(TreeData, v1.EquipmentTreeData{
			GroupId:   value.ID,
			IsClient:  false,
			GroupName: value.Name,
			Label:     value.Name,
			Total:     len(eData),
			Online:    onlineNum,
			Children:  TeData,
		})
	}

	return TreeData, nil
}

func (s *equipmentService) CheckEquipmentStatus() error {
	// 使用 MQTT 连接状态检查设备在线状态
	return s.CheckEquipmentStatusByMQTT()
}

// CheckEquipmentStatusByMQTT 基于 MQTT 连接状态检查设备在线状态
func (s *equipmentService) CheckEquipmentStatusByMQTT() error {
	// 获取所有设备
	allEquipments, err := s.equipmentRepository.ScanEquipmentTime()
	if err != nil {
		return err
	}

	// 获取当前 MQTT 连接的客户端
	connectedClients := s.getMQTTConnectedClients()

	// 创建连接状态映射
	connectedMap := make(map[string]bool)
	for _, clientID := range connectedClients {
		connectedMap[clientID] = true
	}

	// 更新设备状态
	for _, equipment := range allEquipments {
		var newStatus int
		if connectedMap[equipment.MacAddress] {
			newStatus = 1 // 在线
		} else {
			newStatus = 2 // 离线
		}

		// 只有状态发生变化时才更新数据库
		if equipment.Status != newStatus {
			if err := s.equipmentRepository.SetEquipmentStatusByMacAddress(equipment.MacAddress, newStatus); err != nil {
				s.logger.Error("failed to update equipment status",
					zap.String("mac_address", equipment.MacAddress),
					zap.Int("new_status", newStatus),
					zap.Error(err))
				continue
			}
			s.logger.Info("equipment status updated",
				zap.String("mac_address", equipment.MacAddress),
				zap.Int("old_status", equipment.Status),
				zap.Int("new_status", newStatus))
		}
	}
	return nil
}

// getMQTTConnectedClients 获取当前 MQTT 连接的客户端列表
func (s *equipmentService) getMQTTConnectedClients() []string {
	if s.mqttClient == nil {
		s.logger.Warn("MQTT client is nil, returning empty client list")
		return []string{}
	}
	return s.mqttClient.GetConnectedClients()
}

func (s *equipmentService) EditEquipment(ctx *gin.Context, req v1.SaveEquipmentListRequest) error {
	return s.equipmentRepository.Edit(ctx, req)
}

func (s *equipmentService) GenerateRegistrationCode(macAddress string) (string, error) {
	// 1. 标准化 MAC 地址
	standardizedMac := standardizeMac(macAddress)
	if !isValidMac(standardizedMac) {
		return "", fmt.Errorf("无效的 MAC 地址")
	}

	// 2. 计算 HMAC-SHA256
	salt := "fsMufong" // 通过广播获取的salt字段解密的字符串
	hmacDigest := calculateHmac(sha256.New, []byte(salt), []byte(standardizedMac))

	// 3. 格式化注册码
	hexDigest := strings.ToUpper(hex.EncodeToString(hmacDigest))
	shortHex := hexDigest[4:12]
	if len(shortHex) < 8 {
		return "", fmt.Errorf("生成的注册码长度不足")
	}
	registrationCode := shortHex[:8]

	return registrationCode, nil
}

// 标准化 MAC 地址的函数
func standardizeMac(mac string) string {
	mac = strings.ToUpper(strings.ReplaceAll(mac, ":", ""))
	if len(mac) != 12 {
		return ""
	}
	return mac
}

// 验证 MAC 地址格式是否正确
func isValidMac(mac string) bool {
	if len(mac) != 12 {
		return false
	}
	_, err := strconv.ParseInt(mac, 16, 64)
	return err == nil
}

// 计算 HMAC
func calculateHmac(hashFunc func() hash.Hash, key []byte, data []byte) []byte {
	h := hmac.New(hashFunc, key)
	h.Write(data)
	return h.Sum(nil)
}

func (s *equipmentService) GroupNameList(ctx *gin.Context) ([]v1.EquipmentGroupData, error) {
	return s.equipmentRepository.GroupNameList(ctx)
}

func (s *equipmentService) DeleteGroup(ctx *gin.Context, groud_id int) error {
	return s.equipmentRepository.DeleteGroup(ctx, groud_id)
}

func (s *equipmentService) AddGroup(ctx *gin.Context, req v1.GroupRequest) error {
	return s.equipmentRepository.AddGroup(ctx, req)
}

func (s *equipmentService) GetEquipment(ctx *gin.Context, address string) (v1.EquipmentTree, error) {
	return s.equipmentRepository.GetEquipment(ctx, address)
}

// ProcessEquipmentReport 处理单个设备上报数据
func (s *equipmentService) ProcessEquipmentReport(ctx context.Context, report v1.ReportedData) error {
	// 将上报数据转换为数据库模型
	equipmentReport := &model.EquipmentReport{
		MacAddress:  report.MacAddress,
		DeviceAlias: report.DeviceAlias,
		GroupName:   report.GroupName,
		Timestamp:   report.Timestamp,
		ReportType:  report.ReportType,
		OperationID: report.OperationID,
	}

	// 处理具体的数据部分
	// 这里可以根据report_type做特殊处理
	dataBytes, _ := json.Marshal(report.Data)
	equipmentReport.Data = string(dataBytes)

	return s.equipmentRepository.ProcessEquipmentReport(equipmentReport)
}

// ProcessEquipmentReportBatch 处理批量设备上报数据
func (s *equipmentService) ProcessEquipmentReportBatch(ctx context.Context, reports []v1.ReportedData) error {
	// 转换批量数据
	var equipmentReports []*model.EquipmentReport
	for _, report := range reports {
		equipmentReport := &model.EquipmentReport{
			MacAddress:  report.MacAddress,
			DeviceAlias: report.DeviceAlias,
			GroupName:   report.GroupName,
			Timestamp:   report.Timestamp,
			ReportType:  report.ReportType,
			OperationID: report.OperationID,
		}

		// 处理具体的数据部分
		dataBytes, _ := json.Marshal(report.Data)
		equipmentReport.Data = string(dataBytes)

		equipmentReports = append(equipmentReports, equipmentReport)
	}

	return s.equipmentRepository.ProcessEquipmentReportBatch(equipmentReports)
}

// CleanOldEquipmentReports 清理指定天数前的设备上报数据
func (s *equipmentService) CleanOldEquipmentReports(days int) error {
	// 调用模型层清理旧的设备上报数据
	return s.equipmentRepository.CleanOldReports(days)
}

// naturalLess 实现自然语言排序比较
func naturalLess(a, b string) bool {
	// 使用正则表达式分割字符串为文本和数字部分
	re := regexp.MustCompile(`(\d+|\D+)`)
	aParts := re.FindAllString(a, -1)
	bParts := re.FindAllString(b, -1)

	minLen := len(aParts)
	if len(bParts) < minLen {
		minLen = len(bParts)
	}

	for i := 0; i < minLen; i++ {
		aPart := aParts[i]
		bPart := bParts[i]

		// 检查是否都是数字
		aNum, aIsNum := strconv.Atoi(aPart)
		bNum, bIsNum := strconv.Atoi(bPart)

		if aIsNum == nil && bIsNum == nil {
			// 都是数字，按数值比较
			if aNum != bNum {
				return aNum < bNum
			}
		} else {
			// 至少有一个不是数字，按字符串比较
			if aPart != bPart {
				return aPart < bPart
			}
		}
	}

	// 如果前面的部分都相同，长度短的排在前面
	return len(aParts) < len(bParts)
}

func (s *equipmentService) SendCommand(ctx context.Context, req v1.SendCommandRequest) (string, error) {
	// 如果请求中没有 task_id，则生成一个
	if req.TaskID == "" {
		req.TaskID = fmt.Sprintf("task_%d", time.Now().UnixNano())
	}

	// 根据不同的命令类型进行处理
	switch req.Type {
	case 7: // 定时开关机
		// The logic for persisting schedule settings has been moved to SaveEquipmentPowerSchedules.
		// This case now only handles sending the MQTT command to the device.
		s.logger.Info("Sending schedule power command via MQTT", zap.Any("request", req))
	case 8: // 清除定时开关机
		s.logger.Info("Sending clear schedule power command via MQTT", zap.Any("request", req))
	default:
		s.logger.Info("Processing generic command", zap.Any("request", req))
	}

	// 将请求转换为JSON
	payload, err := json.Marshal(req)
	fmt.Println(string(payload), "payloadpayloadpayload")
	if err != nil {
		return "", fmt.Errorf("failed to marshal command request: %w", err)
	}

	// 发布MQTT消息
	topic := fmt.Sprintf("esop/%s/%s", req.GroupName, req.EquipmentAliasName)
	if err := s.mqttClient.Publish(topic, 1, false, payload); err != nil {
		return "", fmt.Errorf("failed to publish mqtt message: %w", err)
	}

	return req.TaskID, nil
}

func (s *equipmentService) BatchPushMaterial(ctx context.Context, req v1.BatchPushMaterialRequest) error {
	// 1. 根据设备ID获取设备信息
	equipment, err := s.equipmentRepository.GetEquipmentById(ctx, req.Id)
	if err != nil {
		return fmt.Errorf("failed to get equipment by id: %w", err)
	}
	if equipment == nil {
		return fmt.Errorf("equipment not found")
	}

	// 2. 根据设备ID获取最新的素材
	material, err := s.equipmentRepository.GetLatestMaterialByEquipmentId(ctx, req.Id)
	if err != nil {
		return fmt.Errorf("failed to get latest material by equipment id: %w", err)
	}
	if material == nil {
		return fmt.Errorf("no material found for this equipment")
	}

	// 3. 构建MQTT消息
	fileTypeMap := map[string]string{
		"image/jpeg":                    "image",
		"image/png":                     "image",
		"image/gif":                     "image",
		"application/pdf":               "pdf",
		"video/mp4":                     "video",
		"video/webm":                    "video",
		"video/avi":                     "video",
		"vidoe/mpeg":                    "video",
		"video/ogg":                     "video",
		"application/vnd.ms-powerpoint": "ppt",
		"application/vnd.openxmlformats-officedocument.presentationml.presentation": "ppt",
		"application/msword": "word",
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document": "word",
		"application/vnd.ms-excel": "excel",
		"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "excel",
	}
	fileType := fileTypeMap[material.ContentType]
	if fileType == "" {
		fileType = "other"
	}

	fmt.Println(equipment, "equipmentequipmentequipment")
	pushData := v1.PushMaterialRequest{
		Type:      1,
		GroupName: equipment.GroupName,
		List: []v1.MaterialMQT{
			{
				DownloadFile:       "assets/media/" + material.Path,
				FileType:           fileType,
				EquipmentAliasName: equipment.AliasName,
				Hash:               material.Hash,
			},
		},
	}

	// 4. 发送MQTT消息
	payload, err := json.Marshal(pushData)
	if err != nil {
		return fmt.Errorf("failed to marshal push material request: %w", err)
	}

	topic := fmt.Sprintf("esop/%s/%s", equipment.GroupName, equipment.AliasName)
	if err := s.mqttClient.Publish(topic, 1, false, payload); err != nil {
		return fmt.Errorf("failed to publish mqtt message: %w", err)
	}

	return nil
}

func (s *equipmentService) GetMaterialByEquipmentId(ctx context.Context, id int) ([]v1.SourceMaterialData, error) {
	return s.equipmentRepository.GetMaterialByEquipmentId(ctx, id)
}

func (s *equipmentService) AssociateMaterial(ctx context.Context, req v1.AssociateMaterialRequest) error {
	return s.equipmentRepository.AssociateMaterial(ctx, req.EquipmentId, req.MaterialId)
}

func (s *equipmentService) DisassociateMaterial(ctx context.Context, req v1.AssociateMaterialRequest) error {
	// 优先处理批量删除
	if len(req.MaterialIds) > 0 {
		materials, err := s.equipmentRepository.DisassociateMaterials(ctx, req.MaterialIds, req.EquipmentId)
		if err != nil {
			return err
		}
		for _, material := range materials {
			if material.Path != "" {
				// 注意：这里的路径需要根据实际情况调整
				filePath := filepath.Join("assets", "media", material.Path)
				if err := os.Remove(filePath); err != nil {
					// 记录日志，但不中断操作
					s.logger.Warn("failed to delete material file", zap.String("path", filePath), zap.Error(err))
				}
			}
		}
		return nil
	}

	// 兼容单个删除
	if req.MaterialId != 0 {
		material, err := s.equipmentRepository.DisassociateMaterial(ctx, req.MaterialId, req.EquipmentId)
		if err != nil {
			return err
		}
		if material != nil && material.Path != "" {
			filePath := filepath.Join("assets", "media", material.Path)
			if err := os.Remove(filePath); err != nil {
				s.logger.Warn("failed to delete material file", zap.String("path", filePath), zap.Error(err))
			}
		}
		return nil
	}

	return errors.New("material_id or material_ids must be provided")
}

func (s *equipmentService) PushMaterial(ctx context.Context, req v1.PushMaterialRequest) error {
	if len(req.List) == 0 {
		return errors.New("material list is empty")
	}

	for _, material := range req.List {
		// to keep payload consistent with what client might expect, we send the whole structure
		// but with only one item in the list.
		singlePushReq := v1.PushMaterialRequest{
			Type:      req.Type,
			GroupName: req.GroupName,
			List:      []v1.MaterialMQT{material},
		}
		payload, err := json.Marshal(singlePushReq)
		if err != nil {
			s.logger.Error("failed to marshal push material request for single device", zap.Error(err), zap.String("alias", material.EquipmentAliasName))
			continue
		}

		topic := fmt.Sprintf("esop/%s/%s", req.GroupName, material.EquipmentAliasName)
		if err := s.mqttClient.Publish(topic, 1, false, payload); err != nil {
			s.logger.Error("failed to publish mqtt message for single device", zap.Error(err), zap.String("topic", topic))
		}
	}

	return nil
}

func (s *equipmentService) UploadScreenshot(ctx context.Context, file *multipart.FileHeader, groupName, equipmentAliasName, taskId string) (string, error) {
	// 1. 保存文件
	uploadDir := filepath.Join("assets", "screenshots")
	if err := os.MkdirAll(uploadDir, os.ModePerm); err != nil {
		return "", fmt.Errorf("failed to create upload directory: %w", err)
	}

	fileName := fmt.Sprintf("%d_%s", time.Now().UnixNano(), file.Filename)
	filePath := filepath.Join(uploadDir, fileName)

	src, err := file.Open()
	if err != nil {
		return "", fmt.Errorf("failed to open uploaded file: %w", err)
	}
	defer src.Close()

	dst, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to create destination file: %w", err)
	}
	defer dst.Close()

	if _, err := io.Copy(dst, src); err != nil {
		return "", fmt.Errorf("failed to save file: %w", err)
	}

	// 2. 保存到数据库
	screenshot := &model.EquipmentScreenshot{
		GroupName:          groupName,
		EquipmentAliasName: equipmentAliasName,
		ImageURL:           filePath,
		TaskID:             taskId,
	}

	if err := s.equipmentRepository.CreateEquipmentScreenshot(ctx, screenshot); err != nil {
		return "", fmt.Errorf("failed to save screenshot record: %w", err)
	}

	// 3. 返回可访问的URL
	baseURL := "" // This should be configured
	imageURL := baseURL + filePath

	return imageURL, nil
}

func (s *equipmentService) GetLatestScreenshotByEquipment(ctx context.Context, groupName, equipmentAliasName string) (*v1.LatestScreenshotResponse, error) {
	screenshot, err := s.equipmentRepository.GetLatestScreenshotByEquipment(ctx, groupName, equipmentAliasName)
	if err != nil {
		return nil, err
	}

	// 判断截图是否在5秒内创建
	if time.Since(screenshot.CreatedAt).Seconds() <= 5 {
		response := &v1.LatestScreenshotResponse{
			ID:                 screenshot.ID,
			GroupName:          screenshot.GroupName,
			EquipmentAliasName: screenshot.EquipmentAliasName,
			ImageURL:           screenshot.ImageURL,
		}

		return response, nil
	}

	// 如果截图超过5秒，也返回空数据而不是nil
	return nil, nil
}

func (s *equipmentService) DeleteEquipmentScreenshot(ctx context.Context, id int64) error {
	// 1. 获取截图信息，以便获取图片路径
	screenshot, err := s.equipmentRepository.GetEquipmentScreenshotByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get screenshot record: %w", err)
	}
	if screenshot == nil {
		return errors.New("screenshot not found")
	}

	// 2. 删除数据库记录
	if err := s.equipmentRepository.DeleteEquipmentScreenshot(ctx, id); err != nil {
		return fmt.Errorf("failed to delete screenshot record: %w", err)
	}

	// 3. 删除文件
	if err := os.Remove(screenshot.ImageURL); err != nil {
		// 如果文件删除失败，记录日志，但不返回错误，因为数据库记录已经删除
		s.logger.Warn("failed to delete screenshot file", zap.String("file", screenshot.ImageURL), zap.Error(err))
	}

	return nil
}

func (s *equipmentService) GetScreenshotByTaskId(ctx context.Context, taskId string) (*v1.LatestScreenshotResponse, error) {
	screenshot, err := s.equipmentRepository.GetScreenshotByTaskId(ctx, taskId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // Not an error, just no record yet
		}
		return nil, err
	}
	if screenshot == nil {
		return nil, nil
	}

	response := &v1.LatestScreenshotResponse{
		ID:                 screenshot.ID,
		GroupName:          screenshot.GroupName,
		EquipmentAliasName: screenshot.EquipmentAliasName,
		ImageURL:           screenshot.ImageURL,
		CreatedAt:          screenshot.CreatedAt.Unix(),
	}
	return response, nil
}

func (s *equipmentService) GetEquipmentLog(ctx context.Context, req v1.EquipmentLogRequest) (int64, []*model.EquipmentReport, error) {
	return s.equipmentRepository.GetEquipmentLog(ctx, req)
}

func (s *equipmentService) GetLatestDeviceReport(ctx context.Context, req v1.LatestDeviceReportRequest) (map[string]*model.EquipmentReport, error) {
	if len(req.MacAddresses) == 0 {
		return make(map[string]*model.EquipmentReport), nil
	}
	return s.equipmentRepository.FindLatestReportByMacs(ctx, req.MacAddresses)
}

func (s *equipmentService) ClearEquipmentLog(ctx context.Context, req v1.ClearEquipmentLogRequest) error {
	return s.equipmentRepository.ClearEquipmentLog(ctx, req.MacAddress)
}

func (s *equipmentService) SaveEquipmentPowerSchedules(ctx context.Context, req v1.SaveSchedulePowerRequest) error {
	// 1. 将请求中的时间范围转换为模型
	var schedules []*model.EquipmentPowerSchedule
	for _, timeRange := range req.TimeRanges {
		weekdaysJSON, err := json.Marshal(timeRange.Weekdays)
		if err != nil {
			s.logger.Error("Failed to marshal weekdays", zap.Error(err))
			// 根据业务需求，可以选择跳过这个错误项或返回错误
			continue
		}

		schedules = append(schedules, &model.EquipmentPowerSchedule{
			PowerOnTime:  timeRange.PowerOnTime,
			PowerOffTime: timeRange.PowerOffTime,
			Weekdays:     string(weekdaysJSON),
			Enabled:      timeRange.Enabled,
		})
	}

	// 2. 批量保存
	err := s.equipmentRepository.SaveEquipmentPowerSchedules(ctx, req.EquipmentID, schedules)
	if err != nil {
		s.logger.Error("Failed to save equipment power schedules", zap.Error(err))
		return err
	}

	// 3. (可选) 发送MQTT通知设备更新其调度
	// 这一步取决于设备是否需要实时通知来重新加载调度
	// 如果设备在每次启动或固定间隔后会自己拉取调度，则此步骤可以省略
	// ... MQTT publish logic ...

	return nil
}

func (s *equipmentService) GetEquipmentPowerSchedules(ctx context.Context, equipmentId int) ([]*model.EquipmentPowerSchedule, error) {
	return s.equipmentRepository.GetEquipmentPowerSchedules(ctx, equipmentId)
}

func (s *equipmentService) BatchDeleteFiles(ctx context.Context, req v1.BatchDeleteFilesRequest) error {
	// 在这里实现批量删除文件的逻辑
	// 例如，调用 repository 层的方法
	return s.equipmentRepository.BatchDeleteFiles(ctx, req.Days)
}

func (s *equipmentService) GetCurrentOpenFiles(ctx context.Context, req v1.GetCurrentOpenFilesRequest) (*v1.GetCurrentOpenFilesResponse, error) {
	if len(req.MacAddresses) == 0 {
		return &v1.GetCurrentOpenFilesResponse{Files: []v1.CurrentOpenFileInfo{}}, nil
	}

	// 直接查询current_open_file类型的最新报告
	reports, err := s.equipmentRepository.FindLatestCurrentOpenFiles(ctx, req.MacAddresses)
	if err != nil {
		return nil, err
	}

	var files []v1.CurrentOpenFileInfo
	for macAddress, report := range reports {
		var fileInfo v1.CurrentOpenFileInfo
		fileInfo.MacAddress = macAddress
		fileInfo.Timestamp = report.Timestamp

		// 解析JSON数据
		if report.Data != "" {
			var data map[string]interface{}
			if err := json.Unmarshal([]byte(report.Data), &data); err == nil {
				if fileName, ok := data["file_name"].(string); ok {
					fileInfo.FileName = fileName
				}
				if filePath, ok := data["file_path"].(string); ok {
					fileInfo.FilePath = filePath
				}
				if fileType, ok := data["file_type"].(string); ok {
					fileInfo.FileType = fileType
				}
			}
		}

		files = append(files, fileInfo)
	}

	return &v1.GetCurrentOpenFilesResponse{Files: files}, nil
}
