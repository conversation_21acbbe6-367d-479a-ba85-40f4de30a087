package service

import (
	"context"
	"encoding/json"

	v1 "esop/api/v1"
	"esop/internal/model"
	"esop/internal/repository"

	"github.com/gin-gonic/gin"
)

type TemplateService interface {
	AddTemplate(ctx *gin.Context, v *v1.SaveTemplateListRequest) error
	GetTemplate(id int64) (*model.Template, error)
	GetTemplateList(ctx context.Context, req v1.TemplateListRequest) (int64, []*v1.TemplateData, error)
	DeleteTemplate(ctx context.Context, id int) error
	GetDetail(ctx context.Context, id int) (v1.TemplateData, error)
	EditTemplateInfo(ctx context.Context, id int, req v1.SaveTemplateListRequest) error
	AddPack(ctx *gin.Context, v *v1.SavePackRequest) error
	GetMaterialTree(ctx context.Context) ([]*v1.MaterialGroupNode, error)
}

func NewTemplateService(service *Service, templateRepository repository.TemplateRepository, materialGroupRepository repository.MaterialGroupRepository, sourceMaterialRepository repository.SourceMaterialRepository) TemplateService {
	return &templateService{
		Service:                  service,
		templateRepository:       templateRepository,
		materialGroupRepository:  materialGroupRepository,
		sourceMaterialRepository: sourceMaterialRepository,
	}
}

type templateService struct {
	*Service
	templateRepository       repository.TemplateRepository
	materialGroupRepository  repository.MaterialGroupRepository
	sourceMaterialRepository repository.SourceMaterialRepository
}

func (s *templateService) GetTemplate(id int64) (*model.Template, error) {
	return s.templateRepository.FirstById(id)
}

func (s *templateService) GetTemplateList(ctx context.Context, req v1.TemplateListRequest) (int64, []*v1.TemplateData, error) {
	total, info, err := s.templateRepository.GetTemplateList(ctx, req)
	if err != nil {
		return 0, nil, err
	}

	return total, info, nil
}

func (s *templateService) DeleteTemplate(ctx context.Context, id int) error {
	return s.templateRepository.Delete(ctx, id)
}

func (s *templateService) AddTemplate(ctx *gin.Context, v *v1.SaveTemplateListRequest) error {
	return s.templateRepository.Create(ctx, v)
}

func (s *templateService) GetDetail(ctx context.Context, id int) (v1.TemplateData, error) {
	templateData, err := s.templateRepository.GetTemplateDetail(ctx, id)
	if err != nil {
		return templateData, err
	}
	for i := range templateData.TemplateSmDetail {
		var multiFiles []v1.MultiFiles
		if templateData.TemplateSmDetail[i].MultiFilesStr != "" {
			if err := json.Unmarshal([]byte(templateData.TemplateSmDetail[i].MultiFilesStr), &multiFiles); err != nil {
				// Handle error, maybe log it or set a default value
				multiFiles = []v1.MultiFiles{}
			}
		} else {
			multiFiles = []v1.MultiFiles{}
		}
		templateData.TemplateSmDetail[i].MultiFiles = multiFiles
	}
	return templateData, nil
}

func (s *templateService) EditTemplateInfo(ctx context.Context, id int, req v1.SaveTemplateListRequest) error {
	return s.templateRepository.Edit(ctx, id, req)
}

func (s *templateService) AddPack(ctx *gin.Context, v *v1.SavePackRequest) error {
	return s.templateRepository.AddPack(ctx, v)
}

func (s *templateService) GetMaterialTree(ctx context.Context) ([]*v1.MaterialGroupNode, error) {
	// 1. 获取所有分组
	groups, err := s.materialGroupRepository.GetAllGroups(ctx)
	if err != nil {
		return nil, err
	}

	// 2. 获取所有素材
	_, materials, err := s.sourceMaterialRepository.GetSourceMaterialList(ctx, v1.SourceMaterialListRequest{
		PagingRequest: v1.PagingRequest{
			Page:     1,
			PageSize: 999999,
		},
	})
	if err != nil {
		return nil, err
	}

	// 3. 构建分组ID到分组节点的映射
	groupMap := make(map[int]*v1.MaterialGroupNode)
	var tree []*v1.MaterialGroupNode

	for _, group := range groups {
		node := &v1.MaterialGroupNode{
			ID:          group.ID,
			Name:        group.Name,
			Description: group.Description,
			Color:       group.Color,
			SortOrder:   group.SortOrder,
			Children:    []*v1.SourceMaterialNode{},
		}
		groupMap[group.ID] = node
		tree = append(tree, node)
	}

	// 4. 将素材分配到对应的分组
	var ungroupedMaterials []*v1.SourceMaterialNode
	for _, material := range materials {
		materialNode := &v1.SourceMaterialNode{
			ID:           material.ID,
			Name:         material.Name,
			Type:         material.Type,
			Path:         material.Path,
			SourceWidth:  material.SourceWidth,
			SourceHeight: material.SourceHeight,
			Size:         material.Size,
			GroupId:      material.GroupId,
		}

		if groupNode, ok := groupMap[material.GroupId]; ok {
			groupNode.Children = append(groupNode.Children, materialNode)
		} else {
			// 如果 group_id 为 0 或找不到对应的分组，则视为未分组
			ungroupedMaterials = append(ungroupedMaterials, materialNode)
		}
	}

	// 5. 如果存在未分组的素材，创建一个“未分组”的节点
	if len(ungroupedMaterials) > 0 {
		ungroupedNode := &v1.MaterialGroupNode{
			ID:       0,
			Name:     "未分组",
			Children: ungroupedMaterials,
		}
		tree = append([]*v1.MaterialGroupNode{ungroupedNode}, tree...)
	}

	return tree, nil
}
