package service

import (
	"testing"

	"esop/pkg/jwt"

	"go.uber.org/zap"
)

// mockMqttClient 是一个用于测试的 MQTT 客户端模拟
type mockMqttClient struct {
	connectedClients []string
}

func (m *mockMqttClient) Publish(topic string, qos byte, retained bool, payload interface{}) error {
	return nil
}

func (m *mockMqttClient) Disconnect(quiesce uint) {
	// no-op
}

func (m *mockMqttClient) IsConnected() bool {
	return true
}

func (m *mockMqttClient) GetConnectedClients() []string {
	return m.connectedClients
}

func TestGetMQTTConnectedClients(t *testing.T) {
	// 测试获取 MQTT 连接客户端的功能
	connectedClients := []string{"aa:bb:cc:dd:ee:01", "aa:bb:cc:dd:ee:02"}
	mockMqtt := &mockMqttClient{connectedClients: connectedClients}

	// 创建服务
	logger := zap.NewNop()
	jwtService := &jwt.JWT{}
	service := NewService(logger, jwtService, mockMqtt)
	equipmentService := &equipmentService{
		Service:    service,
		mqttClient: mockMqtt,
	}

	// 执行测试
	clients := equipmentService.getMQTTConnectedClients()

	// 验证结果
	if len(clients) != len(connectedClients) {
		t.Errorf("Expected %d connected clients, got %d", len(connectedClients), len(clients))
	}

	for i, client := range clients {
		if client != connectedClients[i] {
			t.Errorf("Client mismatch at index %d: expected %s, got %s", i, connectedClients[i], client)
		}
	}
}

func TestGetMQTTConnectedClientsWithNilClient(t *testing.T) {
	// 测试 MQTT 客户端为 nil 的情况
	logger := zap.NewNop()
	jwtService := &jwt.JWT{}
	service := NewService(logger, jwtService, nil)
	equipmentService := &equipmentService{
		Service:    service,
		mqttClient: nil,
	}

	// 执行测试
	clients := equipmentService.getMQTTConnectedClients()

	// 验证结果 - 应该返回空切片
	if len(clients) != 0 {
		t.Errorf("Expected empty client list when MQTT client is nil, got %d clients", len(clients))
	}
}
