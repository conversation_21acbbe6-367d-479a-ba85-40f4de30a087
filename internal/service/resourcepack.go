package service

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	v1 "esop/api/v1"
	"esop/internal/model"
	"esop/internal/repository"
	//"github.com/gin-gonic/gin"
)

type ResourcePackService interface {
	GetResourcePack(ctx context.Context, id int64) (*model.ResourcePack, error)
	GetResourcePackList(ctx context.Context, req v1.ResourcePackListRequest) (int64, []*v1.ResourcePackListData, error)
	DeleteResourcePack(ctx context.Context, id int) error
	Pub(ctx context.Context, req v1.PubRequest) error
	PubByPackName(ctx context.Context, req v1.PubByPackNameRequest) error
}

func NewResourcePackService(service *Service, resourcePackRepository repository.ResourcePackRepository, equipmentRepository repository.EquipmentRepository) ResourcePackService {
	return &resourcePackService{
		Service:                service,
		resourcePackRepository: resourcePackRepository,
		equipmentRepository:    equipmentRepository,
	}
}

type resourcePackService struct {
	*Service
	resourcePackRepository repository.ResourcePackRepository
	equipmentRepository    repository.EquipmentRepository
}

func (s *resourcePackService) GetResourcePack(ctx context.Context, id int64) (*model.ResourcePack, error) {
	return s.resourcePackRepository.FirstById(ctx, id)
}

func (s *resourcePackService) GetResourcePackList(ctx context.Context, req v1.ResourcePackListRequest) (int64, []*v1.ResourcePackListData, error) {
	total, info, err := s.resourcePackRepository.GetResourcePackList(ctx, req)
	if err != nil {
		return 0, nil, err
	}

	return total, info, nil
}

func (s *resourcePackService) DeleteResourcePack(ctx context.Context, id int) error {
	return s.resourcePackRepository.Delete(ctx, id)
}

// Pub 方法根据请求的 Type 字段处理不同类型的消息，并将处理后的数据发布到 MQTT 服务器
func (s *resourcePackService) Pub(ctx context.Context, req v1.PubRequest) error {
	return s.publishMessage(ctx, req)
}

// publishMessage 方法封装了根据不同条件推送消息的逻辑
// publishMessage 方法封装了根据不同条件推送消息的逻辑
func (s *resourcePackService) publishMessage(ctx context.Context, req v1.PubRequest) error {
	if req.Type == 1 {
		// Type 1: partial send
		if len(req.List) == 0 {
			return fmt.Errorf("req.List is empty for Type 1")
		}
		equipmentArr, err := s.equipmentRepository.GetEquipmentsByIds(ctx, req.List[0].EquipmentId)
		if err != nil {
			return fmt.Errorf("failed to get equipments by ids for Type 1: %w", err)
		}

		for _, equipment := range equipmentArr {
			group, err := s.equipmentRepository.GetGroupsById(ctx, strconv.Itoa(equipment.GroupId))
			if err != nil {
				return fmt.Errorf("failed to get group by id %d: %w", equipment.GroupId, err)
			}
			if len(group) == 0 {
				return fmt.Errorf("group with id %d not found", equipment.GroupId)
			}
			if err := s.sendToEquipment(ctx, req, group[0].Name, equipment.AliasName); err != nil {
				return err
			}
		}
		return nil
	}

	if req.Type == 2 {
		// Type 2 is send to all
		groups, err := s.equipmentRepository.GetEquipmentGroupData()
		if err != nil {
			return fmt.Errorf("failed to get equipment group data: %w", err)
		}
		for _, group := range groups {
			equipments, err := s.equipmentRepository.GetEquipmentData(ctx, group.ID)
			if err != nil {
				return fmt.Errorf("failed to get equipment data for group %s: %w", group.Name, err)
			}
			for _, equipment := range equipments {
				if err := s.sendToEquipment(ctx, req, group.Name, equipment.AliasName); err != nil {
					return err
				}
			}
		}
		return nil
	}

	// Type 3: send by rule
	// Get the group since it's not a wildcard
	if req.Type == 3 {
		fmt.Print(req, "reqreqreqreq")
		if req.List[0].HasStatus != "" {
			var hasSstatus int
			if req.List[0].HasStatus == "online" {
				hasSstatus = 1
			} else if req.List[0].HasStatus == "offline" {
				hasSstatus = 0
			} else {
				return fmt.Errorf("invalid status: %s", req.List[0].HasStatus)
			}

			arrayArray, err := s.equipmentRepository.GetEquipmentStatus(ctx, hasSstatus)
			if err != nil {
				return fmt.Errorf("failed to get equipment status: %w", err)
			}

			for _, item := range arrayArray {
				if err := s.sendToEquipment(ctx, req, item.GroupName, item.AliasName); err != nil {
					return err
				}
			}
		}

		if len(req.List[0].GroupIds) != 0 {

			equipments, err := s.equipmentRepository.GetEquipmentDatas(ctx, req.List[0].GroupIds)
			if err != nil {
				return fmt.Errorf("failed to get equipment data: %w", err)
			}
			// if alias is an array
			for _, item := range equipments {
				if err := s.sendToEquipment(ctx, req, item.GroupName, item.AliasName); err != nil {
					return err
				}
			}

		}

		return nil
	}

	return fmt.Errorf("invalid Type: %d", req.Type)
}

// sendToEquipment 方法发送消息到指定的设备
func (s *resourcePackService) sendToEquipment(ctx context.Context, req v1.PubRequest, groupName, aliasName string) error {
	var sendData v1.PubReSourceBackSendData
	sendData.Type = req.Type
	sendData.GroupName = groupName

	fileType := "html"
	if len(req.List) > 0 {
		sourceBack, err := s.resourcePackRepository.FirstById(ctx, req.List[0].ResourceId)
		if err != nil {
			return fmt.Errorf("failed to get resource pack by id: %w", err)
		}
		template, err := s.resourcePackRepository.GetTemplate(ctx, sourceBack.TemplateId)
		if err != nil {
			return fmt.Errorf("failed to get template by id: %w", err)
		}
		if template.Type != 1 {
			fileType = "pdf"
		}
		typeData := v1.PubReSourceBackData{
			DownloadFile:       "assets/zippack/" + sourceBack.PackName + ".zip",
			FileType:           fileType,
			EquipmentAliasName: aliasName,
			Hash:               sourceBack.Hash,
		}
		sendData.List = append(sendData.List, typeData)
	}

	return s.resourcePackRepository.Pub(ctx, sendData)
}

// getEquipmentAliasAndGroupIds 从设备列表中提取设备别名和分组 ID 字符串
func (s *resourcePackService) getEquipmentAliasAndGroupIds(equipmentArr []*model.Equipment) (string, string) {
	var aliasName []string
	var groupId []string
	for _, equipment := range equipmentArr {
		aliasName = append(aliasName, equipment.AliasName)
		groupId = append(groupId, strconv.Itoa(equipment.GroupId))
	}
	equipmentAliasStr := strings.Join(aliasName, ",")
	groupIdStr := strings.Join(groupId, ",")
	return equipmentAliasStr, groupIdStr
}

// getGroupNames 根据分组 ID 字符串获取分组名称字符串
func (s *resourcePackService) getGroupNames(ctx context.Context, groupIds string) (string, error) {
	groupArr, err := s.equipmentRepository.GetGroupsById(ctx, groupIds)
	if err != nil {
		return "", err
	}
	var groupName []string
	for _, group := range groupArr {
		groupName = append(groupName, group.Name)
	}
	groupNameStr := strings.Join(groupName, ",")
	return groupNameStr, nil
}

// PubByPackName 按包名发送资源包
func (s *resourcePackService) PubByPackName(ctx context.Context, req v1.PubByPackNameRequest) error {
	// 根据包名查找资源包
	resourcePack, err := s.resourcePackRepository.FirstByPackName(ctx, req.PackName)
	if err != nil {
		return fmt.Errorf("failed to get resource pack by pack name %s: %w", req.PackName, err)
	}
	if resourcePack == nil {
		return fmt.Errorf("resource pack with pack name %s not found", req.PackName)
	}

	// 构造发送请求，使用现有的Pub方法
	pubReq := v1.PubRequest{
		Type: req.Type,
		List: []v1.PubListRequest{
			{
				ResourceId:    int64(resourcePack.ID),
				EquipmentId:   req.EquipmentIds,
				EquipmentName: req.EquipmentName,
				GroupIds:      req.GroupIds,
				HasStatus:     req.HasStatus,
			},
		},
	}

	// 如果请求中包含Hash，使用请求中的Hash，否则使用数据库中的Hash
	if req.Hash != "" {
		resourcePack.Hash = req.Hash
	}

	// 调用现有的Pub方法
	return s.Pub(ctx, pubReq)
}
