package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	v1 "esop/api/v1"
	"esop/internal/model"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ResourcePackRepository interface {
	FirstById(ctx context.Context, id int64) (*model.ResourcePack, error)
	FirstByPackName(ctx context.Context, packName string) (*model.ResourcePack, error)
	GetTemplate(ctx context.Context, id int) (*model.Template, error)
	GetResourcePackList(context.Context, v1.ResourcePackListRequest) (int64, []*v1.ResourcePackListData, error)
	Delete(ctx context.Context, id int) error
	Create(ctx *gin.Context, v *model.ResourcePack) error
	Pub(ctx context.Context, req v1.PubReSourceBackSendData) error
}

func NewResourcePackRepository(repository *Repository) ResourcePackRepository {
	repo := &resourcePackRepository{
		Repository:             repository,
		OperationLogRepository: NewOperationLogRepository(repository),
	}

	return repo
}

type resourcePackRepository struct {
	*Repository
	OperationLogRepository OperationLogRepository
}

func (r *resourcePackRepository) FirstById(ctx context.Context, id int64) (*model.ResourcePack, error) {
	var resourcePack model.ResourcePack
	// TODO: query db
	err := r.DB(ctx).Where("id = ?", id).Where("is_deleted = 1").Find(&resourcePack).Error
	if err != nil {
		r.logger.Error("get apply authorize contact by id fail", zap.Any("err", err), zap.Any("id", id))
		return nil, err
	}
	return &resourcePack, nil
}

func (r *resourcePackRepository) GetResourcePackList(ctx context.Context, request v1.ResourcePackListRequest) (int64, []*v1.ResourcePackListData, error) {
	var (
		resourcePackList []*v1.ResourcePackListData
		total            int64 = 10
		err              error
	)
	db := r.DB(ctx).Table("resource_pack").
		Where("is_deleted = 1").
		Order("id DESC").
		Select("*").
		Count(&total)

	if request.Name != "" {
		db.Where("name like ?", "%"+request.Name+"%")
	}
	if request.PackName != "" {
		db.Where("number like ?", "%"+request.PackName+"%")
	}

	if request.CreatedAtStart != 0 {
		db.Where("created_at >=  ?", request.CreatedAtStart)
	}
	if request.CreatedAtEnd != 0 {
		db.Where("created_at <= ?", request.CreatedAtEnd)
	}
	err = db.Scopes(r.Paginate(&request.PagingRequest)).Find(&resourcePackList).Error
	if err != nil {
		return total, nil, err
	}
	return total, resourcePackList, nil
}

func (r *resourcePackRepository) Delete(ctx context.Context, id int) error {
	now := time.Now().Unix()
	update := &model.Template{
		IsDeleted: 2,
		UpdatedAt: now,
	}
	if err := r.DB(ctx).Table("resource_pack").Where("id = ?", id).Updates(update).Error; err != nil {
		return err
	}
	return nil
}

func (r *resourcePackRepository) FirstByPackName(ctx context.Context, packName string) (*model.ResourcePack, error) {
	var resourcePack model.ResourcePack
	err := r.DB(ctx).Where("pack_name = ?", packName).Where("is_deleted = 1").Find(&resourcePack).Error
	if err != nil {
		r.logger.Error("get resource pack by pack name fail", zap.Any("err", err), zap.Any("packName", packName))
		return nil, err
	}
	return &resourcePack, nil
}

func (r *resourcePackRepository) GetTemplate(ctx context.Context, templateId int) (*model.Template, error) {
	var template model.Template
	// TODO: query db
	err := r.DB(ctx).Where("id = ?", templateId).Where("is_deleted = 1").Find(&template).Error
	if err != nil {
		r.logger.Error("get apply authorize contact by id fail", zap.Any("err", err), zap.Any("id", templateId))
		return nil, err
	}
	return &template, nil
}

func (r *resourcePackRepository) Create(ctx *gin.Context, v *model.ResourcePack) error {
	if err := r.DB(ctx).Create(v).Error; err != nil {
		return err
	}
	r.OperationLogRepository.AddOperationLogData(ctx, v.ID, v1.ActionCreate, v1.ModuleResourcePack)
	return nil
}

func (r *resourcePackRepository) Pub(ctx context.Context, req v1.PubReSourceBackSendData) error {
	fmt.Println(req, "reqreqreqreqreqreq")
	// Log the entire operation once
	fullPayload, err := json.Marshal(req)
	if err != nil {
		r.logger.Error("failed to marshal full request", zap.Error(err))
		// return err if you want to stop execution
	} else {
		now := time.Now().Unix()
		sendLogData := &model.SendLog{
			SendContent: string(fullPayload),
			CreatedAt:   now,
			UpdatedAt:   now,
		}
		if err := r.DB(ctx).Create(&sendLogData).Error; err != nil {
			r.logger.Error("failed to save send log", zap.Error(err))
		}
	}

	for _, item := range req.List {
		topic := fmt.Sprintf("esop/%s/%s", req.GroupName, item.EquipmentAliasName)

		// Create a payload for each individual equipment
		singleItemReq := v1.PubReSourceBackSendData{
			Type:      req.Type,
			GroupName: req.GroupName,
			List:      []v1.PubReSourceBackData{item},
		}
		payload, err := json.Marshal(singleItemReq)
		if err != nil {
			r.logger.Error("failed to marshal single item request", zap.Error(err), zap.String("alias", item.EquipmentAliasName))
			continue // Skip to the next item
		}

		err = r.mqtt.Publish(topic, 2, false, payload)
		if err != nil {
			r.logger.Error("failed to publish to mqtt", zap.Error(err), zap.String("topic", topic))
		}
	}

	return nil
}
