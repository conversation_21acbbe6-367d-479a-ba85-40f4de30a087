package mqtt

import (
	"fmt"

	mqtt "github.com/mochi-mqtt/server/v2"
)

// InternalClient defines the interface for our internal MQTT client.
type InternalClient interface {
	Publish(topic string, qos byte, retained bool, payload interface{}) error
	Disconnect(quiesce uint)
	IsConnected() bool
	GetConnectedClients() []string
}

// internalMqttClient provides an in-memory client that publishes directly to the embedded server.
// It is designed to be compatible with the parts of the paho.mqtt.golang Client interface that are used in the application.
type internalMqttClient struct {
	server *mqtt.Server
}

// NewInternalMqttClient creates a new internal MQTT client.
func NewInternalMqttClient(s *mqtt.Server) InternalClient {
	return &internalMqttClient{server: s}
}

// Publish publishes a message directly to the server.
// This method mimics the paho.mqtt.golang's Publish method signature for compatibility.
func (c *internalMqttClient) Publish(topic string, qos byte, retained bool, payload interface{}) error {
	// Convert payload to byte slice
	var p []byte
	switch v := payload.(type) {
	case string:
		p = []byte(v)
	case []byte:
		p = v
	default:
		return fmt.Errorf("unsupported payload type: %T", payload)
	}

	// Publish the packet directly to the server's topic router
	return c.server.Publish(topic, p, retained, qos)
}

// Disconnect is a no-op for the internal client.
func (c *internalMqttClient) Disconnect(quiesce uint) {
	// No operation needed as there is no network connection.
}

// IsConnected always returns true for the internal client.
func (c *internalMqttClient) IsConnected() bool {
	return true
}

// GetConnectedClients returns a list of currently connected client IDs.
func (c *internalMqttClient) GetConnectedClients() []string {
	if c.server == nil || c.server.Clients == nil {
		return []string{}
	}

	// 获取所有连接的客户端
	allClients := c.server.Clients.GetAll()
	connectedClients := make([]string, 0, len(allClients))

	for clientID, client := range allClients {
		// 检查客户端是否仍然连接（未关闭）
		if client != nil && !client.Closed() {
			connectedClients = append(connectedClients, clientID)
		}
	}

	return connectedClients
}
