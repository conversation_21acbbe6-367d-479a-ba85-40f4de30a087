package v1

type EquipmentListRequest struct {
	PagingRequest
	AliasName      string `json:"alias_name" uri:"alias_name" form:"alias_name"`                   // 名称
	DeviceNumber   string `json:"device_number" uri:"device_number" form:"device_number"`          // 编号
	MacAddress     string `json:"mac_address" uri:"mac_address" form:"mac_address"`                // mac地址
	GroupId        int    `json:"group_id" uri:"group_id" form:"group_id"`                         // 分组ID
	CreatedAtStart int64  `json:"created_at_start" uri:"created_at_start" form:"created_at_start"` // 创建时间
	CreatedAtEnd   int64  `json:"created_at_end" uri:"created_at_end" form:"created_at_end"`       // 更新时间
}
type EquipmentListGroupRequest struct {
	PagingRequest
}

type EquipmentData struct {
	ID               int    `json:"id"`
	Name             string `json:"name"`              // 设备名称
	DeviceNumber     string `json:"device_number"`     // 编号
	MacAddress       string `json:"mac_address"`       // mac地址
	RegistrationCode string `json:"registration_code"` // 注册码
	CreatedAt        int64  `json:"created_at"`        // 创建时间
	UpdatedAt        int64  `json:"updated_at"`        // 更新时间
	IsDeleted        int    `json:"is_deleted"`        // 是否删除
	Status           int    `json:"status"`            // 状态
	GroupId          int    `json:"group_id"`          // 分组ID
	GroupName        string `json:"group_name"`        // 分组名称
	AliasName        string `json:"alias_name"`        // 设备别名
}

type EquipmentTreeData struct {
	GroupId   int             `json:"group_id"`   // 分组ID
	GroupName string          `json:"group_name"` // 分组名称
	Label     string          `json:"label"`      // 设备别名
	IsClient  bool            `json:"isClient"`   // 是否是客户端
	Total     int             `json:"total"`      // 设备数量
	Online    int             `json:"online"`     // 在线数量
	Children  []EquipmentTree `json:"children"`
}

type EquipmentTree struct {
	ID            int    `json:"id"`
	IsClient      bool   `json:"isClient"`                                  // 是否是客户端
	IsOnline      bool   `json:"isOnline"`                                  // 是否在线
	DeviceNumber  string `json:"device_number"`                             // 编号
	MacAddress    string `json:"mac_address"`                               // mac地址
	IsDeleted     int8   `json:"is_deleted"`                                // 是否删除
	GroupName     string `json:"group_name"`                                // 分组名称
	AliasName     string `json:"alias_name"`                                // 设备别名
	Status        int8   `json:"status"`                                    // 状态
	UpdatedAt     int64  `json:"updated_at"`                                // 更新时间
	Label         string `json:"label"`                                     // 标签
	IpAddr        string `json:"ip_addr"`                                   // ip
	System        string `json:"system" form:"system"`                      // 系统名称
	SystemVersion string `json:"system_version" form:"system_version" `     // 系统版本
	HardwareInfo  string `json:"hardware_info" form:"hardware_info"`        // 硬件信息
	Manufacturer  string ` json:"manufacturer" form:"manufacturer"`         // 制造商
	Model         string ` json:"model" form:"model"`                       // CPU型号
	Brand         string `gorm:"column:brand" json:"brand"`                 // 品牌
	Device        string `gorm:"column:device" json:"device"`               // 设备名称
	Product       string `gorm:"column:product" json:"product"`             // 产品名称
	AndroidId     string `gorm:"column:android_id" json:"android_id"`       // AndroidId
	Hardware      string `gorm:"column:hardware" json:"hardware"`           // 硬件
	SerialNumber  string `gorm:"column:serial_number" json:"serial_number"` // 序列号
	ScreenWidth   int    `gorm:"column:screen_width" json:"screen_width"`   // 屏幕宽度
	ScreenHeight  int    `gorm:"column:screen_height" json:"screen_height"` // 屏幕高度
}

type SaveEquipmentListRequest struct {
	Name             string `json:"name"  form:"name"`                           // 设备名称
	DeviceNumber     string `json:"device_number"  form:"device_number"`         // 设备编号
	MacAddress       string `json:"mac_address"  form:"mac_address"`             // mac地址
	Status           int    `json:"status"  form:"status"`                       // 状态
	RegistrationCode string `json:"registration_code"  form:"registration_code"` // 注册码
	CreatedAt        int64  `json:"created_at"  form:"created_at"`               // 创建时间
	UpdatedAt        int64  `json:"updated_at"  form:"updated_at"`               // 更新时间
	AliasName        string `json:"alias_name"  form:"alias_name"`               // 设备别名
	IpAddr           string `json:"ip_addr"  form:"ip_addr"`                     // 设备IP
	IsDeleted        int    `json:"is_deleted"  form:"is_deleted"`               // 是否删除
	GroupName        string `json:"group_name"  form:"group_name"`               // 分组名称
	GroupId          int    `json:"group_id"  form:"group_id"`                   // 分组ID
	System           string `json:"system" form:"system"`                        // 系统名称
	SystemVersion    string `json:"system_version" form:"system_version" `       // 系统版本
	HardwareInfo     string `json:"hardware_info" form:"hardware_info"`          // 硬件信息
	Manufacturer     string ` json:"manufacturer" form:"manufacturer"`           // 制造商
	Model            string ` json:"model" form:"model"`                         // CPU型号
	Brand            string `gorm:"column:brand" json:"brand"`                   // 品牌
	Device           string `gorm:"column:device" json:"device"`                 // 设备名称
	Product          string `gorm:"column:product" json:"product"`               // 产品名称
	AndroidId        string `gorm:"column:android_id" json:"android_id"`         // AndroidId
	Hardware         string `gorm:"column:hardware" json:"hardware"`             // 硬件
	SerialNumber     string `gorm:"column:serial_number" json:"serial_number"`   // 序列号
	ScreenWidth      int    `gorm:"column:screen_width" json:"screen_width"`     // 屏幕宽度
	ScreenHeight     int    `gorm:"column:screen_height" json:"screen_height"`   // 屏幕高度
	AppVersion       string `gorm:"column:app_version" json:"app_version"`       /// app版本
}
type SaveEquipmentGroupRequest struct {
	Name      string `json:"name"  form:"name"`     // 分组名称
	Number    int    `json:"number"  form:"number"` // 数量
	CreatedAt int64  `json:"created_at"  form:"created_at"`
	UpdatedAt int64  `json:"updated_at"  form:"updated_at"`
}
type EquipmentGroupData struct {
	ID        int    `json:"id"`     // 分组名称
	Number    int    `json:"number"` // 数量
	Name      string `json:"name"`
	CreatedAt int64  `json:"created_at"`
	UpdatedAt int64  `json:"updated_at"`
}
type GroupRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

// 上报数据基础结构
type ReportedData struct {
	MacAddress       string      `json:"mac_address"`       // 设备MAC地址
	RegistrationCode string      `json:"registration_code"` // 注册码
	DeviceAlias      string      `json:"device_alias"`      // 设备别名
	GroupName        string      `json:"group_name"`        // 组名
	Timestamp        string      `json:"timestamp"`         // 上报时间戳(ISO 8601格式)
	ReportType       string      `json:"report_type"`       // 上报类型
	OperationID      string      `json:"operation_id"`      // 操作唯一标识
	Data             interface{} `json:"data"`              // 根据report_type不同，包含不同的数据结构
}

// 批量上报请求
type ReportedBatchRequest struct {
	Reports []ReportedData `json:"reports"` // 上报数据列表
}

// 上报响应
type ReportedResponse struct {
	ReceivedCount  int `json:"received_count"`  // 接收数量
	ProcessedCount int `json:"processed_count"` // 处理数量
}

type SendCommandRequest struct {
	Type               int      `json:"type"`
	GroupName          string   `json:"group_name"`
	EquipmentAliasName string   `json:"equipment_alias_name"`
	Command            string   `json:"command,omitempty"`
	Version            string   `json:"version,omitempty"`
	OtaUrl             string   `json:"ota_url,omitempty"`
	List               []string `json:"list,omitempty"`
	TaskID             string   `json:"task_id,omitempty"`
	Volume             int      `json:"volume,omitempty"`
	Mute               bool     `json:"mute,omitempty"`
	PowerOnTime        string   `json:"powerOnTime,omitempty"`
	PowerOffTime       string   `json:"powerOffTime,omitempty"`
	Weekdays           string   `json:"weekdays,omitempty"`
}

type AssociateMaterialRequest struct {
	EquipmentId int   `json:"equipment_id"`
	MaterialId  int   `json:"material_id,omitempty"`
	MaterialIds []int `json:"material_ids,omitempty"`
}

type PushMaterialRequest struct {
	Type      int           `json:"type"`
	GroupName string        `json:"group_name"`
	List      []MaterialMQT `json:"list"`
}

type MaterialMQT struct {
	DownloadFile       string `json:"download_file"`
	FileType           string `json:"file_type"`
	EquipmentAliasName string `json:"equipment_alias_name"`
	Hash               string `json:"hash"`
}
type BatchPushMaterialRequest struct {
	Id int `json:"id"`
}

// LatestScreenshotResponse 获取设备最新截图响应
type LatestScreenshotResponse struct {
	ID                 int64  `json:"id"`
	GroupName          string `json:"group_name"`
	EquipmentAliasName string `json:"equipment_alias_name"`
	ImageURL           string `json:"image_url"`
	CreatedAt          int64  `json:"created_at"`
}

type DeleteScreenshotRequest struct {
	ID int64 `json:"id" form:"id" binding:"required"`
}

type EquipmentLogRequest struct {
	PagingRequest
	MacAddress string `json:"mac_address" form:"mac_address" binding:"required"`
}

type SaveSchedulePowerRequest struct {
	EquipmentID int         `json:"equipment_id" binding:"required"`
	TimeRanges  []TimeRange `json:"time_ranges"`
}

type TimeRange struct {
	PowerOnTime  string   `json:"powerOnTime"`
	PowerOffTime string   `json:"powerOffTime"`
	Weekdays     Weekdays `json:"weekdays"`
	Enabled      bool     `json:"enabled"`
}

type Weekdays struct {
	Monday    bool `json:"monday"`
	Tuesday   bool `json:"tuesday"`
	Wednesday bool `json:"wednesday"`
	Thursday  bool `json:"thursday"`
	Friday    bool `json:"friday"`
	Saturday  bool `json:"saturday"`
	Sunday    bool `json:"sunday"`
}

type LatestDeviceReportRequest struct {
	MacAddresses []string `json:"mac_addresses" binding:"required"`
}

type ClearEquipmentLogRequest struct {
	MacAddress string `json:"mac_address" form:"mac_address" binding:"required"`
}

type BatchDeleteFilesRequest struct {
	Days int `json:"days" binding:"required"`
}

type GetCurrentOpenFilesRequest struct {
	MacAddresses []string `json:"mac_addresses" binding:"required"`
}

type CurrentOpenFileInfo struct {
	MacAddress string `json:"mac_address"`
	FileName   string `json:"file_name"`
	FilePath   string `json:"file_path,omitempty"`
	FileType   string `json:"file_type,omitempty"`
	Timestamp  string `json:"timestamp"`
}

type GetCurrentOpenFilesResponse struct {
	Files []CurrentOpenFileInfo `json:"files"`
}
