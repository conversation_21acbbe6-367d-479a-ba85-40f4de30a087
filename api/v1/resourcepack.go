package v1

type ResourcePackListRequest struct {
	PagingRequest
	Name           string `json:"name" uri:"name" form:"name"`                                     // 名称
	PackName       string `json:"pack_name" uri:"pack_name" form:"pack_name"`                      // 包名
	CreatedAtStart int64  `json:"created_at_start" uri:"created_at_start" form:"created_at_start"` // 创建时间
	CreatedAtEnd   int64  `json:"created_at_end" uri:"created_at_end" form:"created_at_end"`       // 更新时间
}
type ResourcePackListData struct {
	ID        int    `json:"id"`
	Name      string `json:"name"`       // 设备名称
	PackName  string `json:"pack_name"`  // 包名
	CreatedAt int64  `json:"created_at"` // 创建时间
	UpdatedAt int64  `json:"updated_at"` // 更新时间
	IsDeleted int8   `json:"is_deleted"` // 是否删除
}
type ResourcePackData struct {
	ID        int    `json:"id"`
	Name      string `json:"name"`       // 设备名称
	PackName  string `json:"pack_name"`  // 包名
	CreatedAt int64  `json:"created_at"` // 创建时间
	UpdatedAt int64  `json:"updated_at"` // 更新时间
	IsDeleted int8   `json:"is_deleted"` // 是否删除
}

//	type PubRequest struct {
//		EquipmentName string `json:"equipment_name"` //设备名称
//		PackName      string `json:"pack_name"`      // 包名
//		Type          int8   `json:"type"`           // 类型 1--部分发送, 2--全部发送 3--按照规则发送
//
// }
// OuterRequest 结构体是外
// 前端传值的定义
type PubRequest struct {
	Type    int              `json:"type"`
	GroupId string           `json:"group_id"` // 组id
	List    []PubListRequest `json:"list"`
}
type PubListRequest struct {
	ResourceId    int64  `json:"resource_id"`    // 包id
	EquipmentId   []int  `json:"equipment_id"`   // 设备id
	GroupIds      []int  `json:"group_ids"`      // 分组id
	HasStatus     string `json:"has_status"`     // 状态
	EquipmentName string `json:"equipment_name"` // 设备别名--按规则发送才需要使用
}

// PubReSourceBackSendData 发送数据
type PubReSourceBackSendData struct {
	Type      int                   `json:"type"`
	GroupName string                `json:"group_name"` // 分组名称
	List      []PubReSourceBackData `json:"list"`
}

type PubReSourceBackData struct {
	DownloadFile string `json:"download_file"` // 资源包路径
	// HasHtml      bool   `json:"has_html"`      //是否有HTML
	FileType string `json:"file_type"` //       hmtl  PDF
	// GroupName          string `json:"group_name"`           // 分组名称
	EquipmentAliasName string `json:"equipment_alias_name"` // 设备
	Hash               string `json:"hash"`                 // 文件hash
}

type SaveSendLogData struct {
	SendContent string `json:"send_content" form:"send_content"` // 发送日志
	CreateAt    int64  `json:"create_at"  form:"create_at"`
	UpdateAt    string `json:"update_at"  form:"update_at"` // 包名称
}

// PubByPackNameRequest 按包名发送请求结构体
type PubByPackNameRequest struct {
	Type          int    `json:"type"`                         // 类型 1--部分发送, 2--全部发送 3--按照规则发送
	PackName      string `json:"pack_name" binding:"required"` // 包名
	Hash          string `json:"hash"`                         // 文件hash
	EquipmentIds  []int  `json:"equipment_ids"`                // 设备id
	GroupIds      []int  `json:"group_ids"`                    // 分组id
	HasStatus     string `json:"has_status"`                   // 状态
	EquipmentName string `json:"equipment_name"`               // 设备别名--按规则发送才需要使用
}
