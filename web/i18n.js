import { login } from "@/api/user";
import Vue from "vue";
import VueI18n from "vue-i18n";
import locale from "element-ui/lib/locale";
import enLocale from "element-ui/lib/locale/lang/en";
import zhLocale from "element-ui/lib/locale/lang/zh-CN";
Vue.use(VueI18n);

const messages = {
  en: {
    public: {
      reset: "Reset",
      search: "Search",
      cancel: "Cancel",
      select: "Select",
      confirm: "Confirm",
      delete: "Delete",
      batchDelete: "Batch Delete",
      edit: "Edit",
      add: "Add",
      operation: "Operation",
      startDate: "Start Date",
      uploadMaterial: "Upload Material",
      endDate: "End Date",
      addSuccess: "Added successfully!",
      editSuccess: "Edited successfully!",
      deleteSuccess: "Deleted successfully!",
      totalItems: "A total of {count} items",
      to: "to",
    },
    common: {
      reverseMessage: "Reverse Message"
    },
    login: {
      esopBackend: "ESOP Admin",
      rememberPassword: "Remember Password",
      login: "Login",
      enterUsername: "Please enter the username",
      enterPassword: "Please enter the password",
      switchLang: "Switch to Chinese",
      loginOut: "Login Out",
      loginSuccess: "Login Success",
    },
    equipmentCenter: {
      actionBar: {
        selectAllOffline: "Select All Offline",
        selectAllOnline: "Select All Online",
        batchUpload: "Batch Upload",
        batchDeleteFiles: "Batch Delete Files",
        batchPush: "Batch Push",
        batchReboot: "Batch Reboot",
        batchShutdown: "Batch Shutdown",
        batchSchedulePower: "Batch Schedule Power",
        adjustVolume: "Adjust Volume",
        batchUploadTooltip: "The uploaded file names must match the device aliases; for example: sop1-2025-05-08.jpg; sop1 is the device alias"
      },
      uploadDialog: {
        title: "Upload files to [{device}]",
        dragText: "Drag files here, or <span class='click-text'>click to select files</span>",
        supportedFormats: "Supports image, video, PDF formats",
        fileList: "File list ({count})",
        clearAll: "Clear all",
        preview: "Preview",
        autoDetectedName: "Auto detected name:",
        totalFiles: "Total files:",
        totalSize: "Total size:",
        uploaded: "Uploaded:",
        cancel: "Cancel",
        uploading: "Uploading...",
        startUpload: "Start upload",
        unsupportedFileType: "Unsupported file type: {file}",
        fileTooLarge: "File too large: {file}, maximum size allowed is 1GB",
        uploadSuccess: "Successfully uploaded {count} files",
        uploadPartialFailure: "{count} files failed to upload",
        uploadFailed: "Upload failed, please try again",
        fileTypes: {
          image: "Image",
          video: "Video",
          pdf: "PDF",
          ppt: "PPT",
          word: "Word",
          excel: "Excel",
          txt: "TXT",
          other: "Other",
          unknown: "Unknown"
        }
      },
      table: {
        num: "Serial Number",
        name: "Device Name",
        number: "Device Number",
        alias_name: "Device Alias",
        deviceAlias: "Device Alias",
        macAddress: "MAC Address",
        connectionStatus: "Connection Status",
        status: "Status",
        online: "Online",
        offline: "Offline",
        model: "Model",
        system: "System",
        system_version: "System Version",
        serial_number: "Serial Number",
        screen: "Screen",
        openStatus: "Open Status",
        pushStatus: "Push Status",
        group_name: "Group Name",
        created_at: "Creation Time",
        updated_at: "Last Online Time",
        ip_addr: "IP Address",
        operation: "Operation",
        edit: "Edit",
        delete: "Delete",
        time: "Time",
        logType: "Log Type",
        logContent: "Log Content",
        sureToDelete: "Are you sure to delete this device?",
      },
      status: {
        offlineStatus: "Offline Status",
        idle: "Idle",
        previewing: "Previewing",
        downloading: "Downloading",
        decompressing: "Extracting",
        executingCommand: "Executing Command",
        uploading: "Uploading",
        previewingFile: "Previewing File"
      },
      dialog: {
        screenshot: "Real-time Screenshot",
        gettingScreenshot: "Getting screenshot...",
        screenshotFailed: "Unable to load screenshot",
        volumeAdjustment: "Adjust Volume",
        powerSchedule: "Power Schedule",
        batchDeleteFiles: "Batch Delete Files",
        selectDeleteDays: "Select Delete Days",
        title: {
          add: "Add",
          edit: "Edit",
          addGroup: "Add Group",
          editGroup: "Edit Group",
        },
        form: {
          accountName: "Account Name",
          account: "Account",
          password: "Password",
          accountNamePlaceholder: "Please enter the account name",
          accountPlaceholder: "Please enter the account",
          passwordPlaceholder: "Please enter the password",
          groupName: "Group Name",
          groupNamePlaceholder: "Please enter group name",
          groupDescription: "Group Description",
          groupDescriptionPlaceholder: "Please enter group description",
        },
        message: {
          deleteGroupConfirm: "Are you sure to delete this group?",
          deleteGroupWarning: "Deleting a group will move all devices in it to 'No Group'. Continue?",
          groupNameRequired: "Group name is required",
          groupAddSuccess: "Group added successfully",
          groupEditSuccess: "Group updated successfully",
          groupDeleteSuccess: "Group deleted successfully",
        },
      },
      message: {
        offlineDevice: "Device is offline, cannot send",
        screenshotCommandSent: "Screenshot command sent to device",
        screenshotCommandFailed: "Failed to send screenshot command",
        otaCommandSent: "OTA upgrade command sent to device",
        otaCommandFailed: "Failed to send OTA upgrade command",
        rebootCommandSent: "Reboot command sent to device",
        rebootCommandFailed: "Failed to send reboot command",
        shutdownCommandSent: "Shutdown command sent to device",
        shutdownCommandFailed: "Failed to send shutdown command",
        batchPushInfo: "Preparing to batch push to {count} devices...",
        batchRebootInfo: "Preparing to send reboot command to {count} devices...",
        batchShutdownInfo: "Preparing to send shutdown command to {count} devices...",
        batchPushSuccess: "All {count} devices have successfully received push commands.",
        batchRebootSuccess: "All {count} devices have successfully received reboot commands.",
        batchShutdownSuccess: "All {count} devices have successfully received shutdown commands.",
        batchOperationPartialSuccess: "{success} devices sent successfully, {failure} devices failed",
        batchOperationFailed: "All devices failed to send.",
        clearLogsSuccess: "Logs cleared successfully!",
        clearLogsFailed: "Failed to clear logs",
        cancelClearLogs: "Clear operation cancelled",
        confirmReboot: "Are you sure you want to remotely reboot device: {device}?",
        confirmShutdown: "Are you sure you want to remotely shutdown device: {device}? This may cause the device to go offline.",
        confirmBatchReboot: "Are you sure you want to batch reboot the selected {count} devices?",
        confirmBatchShutdown: "Are you sure you want to batch shutdown the selected {count} devices? This may cause devices to go offline.",
        cancelBatchReboot: "Batch reboot operation cancelled",
        cancelBatchShutdown: "Batch shutdown operation cancelled",
        selectAtLeastOneDevice: "Please select at least one device",
        selectGroupFirst: "Please select a device group first",
        invalidDevice: "Invalid device",
        saveSuccess: "Saved successfully",
        saveFailed: "Save failed",
        deleteSuccess: "Deleted successfully",
        deleteFailed: "Delete failed",
        confirmDeleteDevice: "Are you sure you want to delete device: {device}?",
        confirmDeleteMaterial: "Are you sure you want to delete material \"{name}\"? This will disassociate the material from the current device.",
        confirmBatchDeleteMaterials: "Are you sure you want to delete the selected {count} materials?",
        batchDeleteSuccess: "Batch delete successful",
        batchDeleteFailed: "Batch delete failed",
        materialSendSuccess: "Material push command sent",
        materialSendFailed: "Material push failed",
        disassociateSuccess: "Material deleted successfully",
        disassociateFailed: "Material delete failed",
        selectAtLeastOneMaterial: "Please select at least one material",
        selectMaterialFirst: "Please select a material first",
        replaceMaterialSuccess: "Material associated successfully",
        replaceMaterialFailed: "Material association failed",
        confirmReplaceMaterial: "Please select a material",
        uploadSuccess: "Upload successful",
        uploadFailed: "Upload failed",
        uploadError: "An error occurred during upload, please check the console for details.",
        uploadPartialSuccess: "{success} files uploaded successfully, {failure} files could not be matched to devices.",
        uploadAllSuccess: "All {count} files have been successfully uploaded and associated.",
        selectDeleteDays: "Please select delete days",
        deleteFilesSuccess: "Delete successful",
        deleteFilesFailed: "Delete failed"
      },
      info: {
        basicInfo: "Basic Information",
        materialManagement: "Material Management",
        deviceLogs: "Device Logs",
        clearLogs: "Clear Logs",
        uploadMaterial: "Upload File",
        remoteControl: "Remote Control",
        realTimeScreenshot: "Real-time Screenshot",
        remoteReboot: "Remote Reboot",
        remoteShutdown: "Remote Shutdown",
        otaUpgrade: "OTA Upgrade",
        enterOtaUrl: "Please enter OTA upgrade file",
        cancelOta: "OTA upgrade cancelled",
        confirmOta: "Confirm",
        cancel: "Cancel",
        replaceMaterial: "Replace Material",
        volumeAdjustment: "Adjust Volume",
        powerSchedule: "Power Schedule",
        batchDeleteFiles: "Batch Delete Files",
        selectDeleteDays: "Select Delete Days",
        addTimeRange: "Add Time Range",
        saveSchedule: "Save",
        clearSchedule: "Clear Schedule"
      },
      carousel: {
        debugInfo: "Carousel: {clientKey} | Index: {currentIndex} | Files: {totalFiles}"
      },
      tree: {
        null: "No Group",
        title: "Device List",
        searchPlaceholder: "Please enter the device name or MAC address",
        selectTip: "Please select the device",
        basicInfo: "Basic Information",
        total: "Total",
        online: "Online",
        offline: "Offline",
        noData: "No device data",
      },
      material: {
        title: "Material & Preview",
        noMaterial: "No material associated with this device"
      },
      form: {
        name: "Name",
        aliasName: "Alias Name",
        number: "Number",
        mac: "MAC Address",
        date: "Time",
        status: "Status",
        namePlaceholder: "Please enter the device name",
        numberPlaceholder: "Please enter the device number",
        groupPlaceholder: "Please select the device group",
        macPlaceholder: "Please enter the MAC address",
      },
      button: {
        deviceEdit: "Edit Device",
        allSend: "Send All",
        create: "Create",
        cancel: "Cancel",
        edit: "Edit",
        editTemplate: "Edit Template",
        delete: "Delete",
        singleSend: "Send",
        upload: "Upload Material",
        replace: "Replace Material",
        preview: "Live Preview",
        addGroup: "Add Group",
        editGroup: "Edit Group",
        deleteGroup: "Delete Group"
      },
    },
    operationLog: {
      form: {
        operatorName: "Name",
        operatorAccount: "Account",
        operationTime: "Time",
        operatorNamePlaceholder: "Please enter the operator name",
        operatorAccountPlaceholder: "Please enter the operator account",
      },
      table: {
        num: "Serial Number",
        operatorName: "Operator Name",
        operatorAccount: "Operator Account",
        action: "Type",
        module: "Module",
        operationTime: "Operation Time",
      },
    },
    material: {
      group: {
        title: "Material Group",
        all: "All Groups"
      },
      groupDialog: {
        title: "Group Management",
        enterGroupName: "Please enter group name",
        groupDescription: "Group description (optional)",
        addGroup: "Add Group",
        existingGroups: "Existing Groups",
        refresh: "Refresh",
        noGroups: "No groups available, please create a group first",
        createTime: "Created:",
        edit: "Edit",
        delete: "Delete",
        close: "Close",
        editGroup: "Edit Group",
        groupName: "Group Name",
        groupDescriptionLabel: "Group Description",
        confirm: "Confirm",
        cancel: "Cancel",
        enterGroupNameRequired: "Please enter group name",
        groupNameLengthError: "Group name length should be 1 to 50 characters",
        groupDescriptionLengthError: "Group description cannot exceed 200 characters",
        loadGroupsFailed: "Failed to load groups",
        groupCreatedSuccess: "Group created successfully",
        createGroupFailed: "Failed to create group",
        groupUpdatedSuccess: "Group updated successfully",
        updateGroupFailed: "Failed to update group",
        confirmDeleteGroup: "Are you sure you want to delete group \"{name}\"?",
        confirmTitle: "Confirm",
        groupDeletedSuccess: "Group deleted successfully",
        deleteGroupFailed: "Failed to delete group"
      },
      button: {
        add: "Add Material",
        upload: "Upload File",
        selectAllCurrent: "Select All Current Page",
        batchDelete: "Batch Delete"
      },
      uploadDialog: {
        title: "Batch Upload Files",
        selectGroupPlaceholder: "Please select group",
        ungrouped: "Ungrouped",
        addNewGroup: "Add New Group",
        dragText: "Drag files here, or <span class='click-text'>click to select files</span>",
        supportedFormats: "Supports image, video, PDF formats",
        fileList: "File list ({count})",
        clearAll: "Clear all",
        preview: "Preview",
        autoDetectedName: "Auto detected name:",
        totalFiles: "Total files:",
        totalSize: "Total size:",
        uploaded: "Uploaded:",
        cancel: "Cancel",
        uploading: "Uploading...",
        startUpload: "Start upload",
        unsupportedFileType: "Unsupported file type: {file}",
        fileTooLarge: "File too large: {file}, maximum size allowed is 1GB",
        uploadSuccess: "Successfully uploaded {count} files",
        uploadPartialFailure: "{count} files failed to upload",
        uploadFailed: "Upload failed, please try again",
        enterGroupName: "Please enter group name",
        addGroup: "Add Group",
        confirm: "Confirm",
        cancelDialog: "Cancel",
        groupNameLengthError: "Group name length should be 1 to 50 characters",
        groupCreatedSuccess: "Group created successfully",
        createGroupFailed: "Failed to create group",
        fileTypes: {
          image: "Image",
          video: "Video",
          pdf: "PDF",
          ppt: "PPT",
          word: "Word",
          excel: "Excel",
          txt: "TXT",
          other: "Other",
          unknown: "Unknown"
        }
      },
      table: {
        num: "Number",
        name: "Name",
        type: "Type",
        preview: "Preview",
        created_at: "Time",
        operation: "Operation",
        sureToDelete: "Are you sure to delete this material?",
        image: "Image",
        video: "Video",
        file: "File",
        deviceAlias: "Device Alias",
        macAddress: "MAC Address",
        connectionStatus: "Connection Status",
        pushStatus: "Push Status"
      },
      status: {
        online: "Online",
        offline: "Offline"
      },
      confirm: {
        delete: "Are you sure you want to delete this material?",
        batchDelete: "Are you sure you want to delete the selected {count} materials?"
      },
      empty: {
        noMaterial: "No materials available"
      },
      form: {
        name: "Name",
        group: "Group",
        groupPlaceholder: "Please select group",
        noGroup: "No Group",
        date: "Time",
        namePlaceholder: "Please enter the material name",
      },
      dialog: {
        title: {
          addMaterial: "Add Material",
          editMaterial: "Edit Material",
        },
        replaceMaterial: "Replace Material",
        editMaterial: "Edit Material",
        form: {
          name: "Name",
          type: "Type",
          path: "Path",
          upload: "Material",
          namePlaceholder: "Please enter the material name",
          typePlaceholder: "Please select the material type",
          pathPlaceholder: "Please upload the material",
          selectFile: "Please upload a file",
          onlyImage: "Please upload image type material",
          onlyVideo: "Please upload video type material",
          onlyPDF: "Please upload PDF type file",
          onlyFile: "Please upload a supported file type (PDF, Word, Excel, PowerPoint)",
          materialName: "Material Name",
          group: "Group",
          groupPlaceholder: "Please select group",
          noGroup: "No Group"
        },
      },
      message: {
        deleteSuccess: "Deleted successfully",
        batchDeleteSuccess: "Batch delete successful",
        editSuccess: "Edited successfully",
        sendSuccess: "Material push command sent"
      },
      error: {
        deleteFailed: "Delete failed",
        batchDeleteFailed: "Batch delete failed",
        editFailed: "Edit failed",
        sendFailed: "Material push failed"
      }
    },
    account: {
      form: {
        name: "Name",
        createTime: "Time",
        account: "Account",
        password: "Password",
        namePlaceholder: "Please enter the account name",
        accountPlaceholder: "Please enter the account",
        passwordPlaceholder: "Please enter the password",
      },
      button: {
        addAccount: "Add Account",
      },
      table: {
        num: "Serial Number",
        name: "Account Name",
        account: "Account",
        created_at: "Creation Time",
        confirmDelete: "Are you sure to delete this account?",
      },
      dialog: {
        title: {
          add: "Add Account",
          edit: "Edit Account",
        },
      },
    },
    template: {
      background: {
        repeat: "Tile",
        cover: "Stretch",
        contain: "Fit",
        auto: "Original Size",
      },
      propertiesPanel: {
        templateProperties: "Template Properties",
        templateName: "Template Name",
        templateNamePlaceholder: "Please enter template name",
        resolution: "Resolution",
        resolutionPlaceholder: "Please select resolution",
        areaProperties: "Area Properties",
        left: "Left",
        top: "Top",
        width: "Width",
        height: "Height",
        currentAreaMaterial: "Current Area Material",
        sort: "Sort",
        time: "Time",
        file: "File",
        operation: "Operation",
        delete: "Delete",
        layerOrder: "Layer Order",
        noMaterialOnPage: "No material on current page",
        timeComponent: "Time Component",
        webComponent: "Web Component",
        unnamedMaterial: "Unnamed Material",
        filesCount: "{count} files",
        emptyArea: "Empty Area",
        textMarqueeProperties: "Text Marquee Properties",
        marqueeText: "Marquee Text",
        fontSize: "Font Size",
        scrollSpeed: "Scroll Speed",
        fontColor: "Font Color",
        backgroundColor: "Background Color"
      },
      form: {
        name: "Name",
        date: "Time",
        namePlaceholder: "Please enter the template name",
        urlPlaceholder: "Please enter the URL",
        resolutionRatio: "Resolution",
        resolutionRatioPlaceholder: "Please select resolution",
        swipterTime: "Switching time",
        resourcePackName: "Resource Pack Name",
        resourcePackAlias: "Resource Pack Alias",
        resourcePackAliasPlaceholder: "Please enter resource pack alias",
        successTips: "Packaging successful",
        resourcePackNamePlaceholder: "Please enter the resource pack name",
        materialsPlaceholder: "Please give me the materials",
        iframeUrl: "Iframe URL",
      },
      button: {
        updateWorkTemplate: "Update Work Template",
        createWorkTemplate: "Create Work Template",
        create: "Create Template",
        addMaterial: "Add Material",
        clearBackground: "Clear Background",
        clearTemplate: "Reset Template",
        prevPage: "Previous Page",
        nextPage: "Next Page",
        addNewPage: "Add New Page",
        setBackground: "Set Background Image",
        addImage: "Add Image",
        addVideo: "Add Video",
        dateTime: "Date & Time",
        iframe: "Iframe",
        page: "Page",
        delPage: "Delete Page",
        addNewPageShort: "Add New Page",
        delPageShort: "Delete Page",
        resetCurrentPage: "Reset Current Page",
        edit: "Edit",
        pack: "Pack",
        delete: "Delete",
        addTextMarquee: "Add Text Marquee",
        textMarquee: "Text Marquee"
      },
      table: {
        num: "Serial Number",
        name: "Template Name",
        type: "Template Type",
        created_at: "Creation Time",
        operation: "Operation",
        sureToDelete: "Are you sure to delete this template?",
      },
      dialog: {
        title: {
          add: "Add Template",
          edit: "Edit Template",
          send: "Send Template",
          material: "Select Material",
          selectImageMulti: "Select Images (Multi-select)",
          selectVideoMulti: "Select Videos (Multi-select)",
          addMaterial: "Add Material",
          iframeDialog: "Iframe Settings",
          packDialog: "Packaging",
        },
        pageInfo: {
          currentPage: "Current Page",
          totalPages: "Total Pages",
          pageDisplay: "Page {current} / {total}",
        },
        materialType: {
          image: "Image",
          video: "Video",
          dateTime: "Date & Time",
          file: "File",
        },
      },
      search: {
        placeholder: "Search by name",
      },
      empty: {
        noTemplates: "No templates available",
      },
      group: {
        title: "Material Groups",
        all: "All Groups",
      },
      confirm: {
        deleteTemplate: "Are you sure you want to delete this template?",
        deleteMaterial: "Are you sure you want to delete this material?",
      },
      message: {
        materialAdded: "Material added successfully",
        dateTimeAdded: "Date & Time component added successfully",
        materialPlaced: "Material successfully placed in {areaType}",
        materialNotMatch: "Material cannot be placed in {areaType}, please select matching material type",
        materialReplaced: "Material replaced in current area",
        materialAddedToCarousel: "Material added to {areaType} carousel ({count} files total)",
        materialTypeMismatch: "Material type does not match area type, cannot replace",
        materialDropError: "Error occurred while placing material",
        layerUpdated: "Material layer updated successfully",
        deleteMaterialFailed: "Failed to delete material",
        materialNotFound: "Material not found",
        dragStart: "Start dragging area",
        resizeStart: "Start resizing area",
        unknownMaterial: "Unknown material",
        timeComponent: "Time Component",
        webComponent: "Web Component",
        unnamedMaterial: "Unnamed material",
        selectedMaterials: "Selected {count} {type}",
        confirmAdd: "Confirm Add ({count})",
        materialInfo: "Material: {name}",
        image: "Image",
        pixels: "px",
        textMarqueeAdded: "Text marquee added successfully"
      },
      tooltip: {
        addImageArea: "Click to add image area",
        addVideoArea: "Click to add video area",
        addTimeComponent: "Click to add time component",
        addWebArea: "Click to add web area",
      },
      select: {
        backgroundSize: "Background display mode",
        backgroundRepeat: "Tile",
        backgroundCover: "Stretch",
        backgroundContain: "Fit",
        backgroundAuto: "Original Size",
      },
      type: {
        normalTemplate: "Normal Template",
        workTemplate: "Work Template",
      },
      send: {
        description: "Please select send method",
        allDevices: "Send All",
        allDevicesDesc: "Send template to all online devices",
        specificDevices: "Send Specific",
        specificDevicesDesc: "Select specific devices to send",
        byRules: "Send by Rules",
        byRulesDesc: "Send automatically according to preset rules",
        allDevicesSuccess: "Successfully sent to all devices",
        selectDevicesFirst: "Please select devices to send first",
        selectRulesFirst: "Please select send rules first",
        sendFailed: "Send failed",
        selectDevices: "Select Devices",
        selectRules: "Select Rules",
        confirmSend: "Confirm Send",
        confirmSendByRule: "Confirm Send by Rule",
        loadGroupsFailed: "Failed to load groups",
        loadDevicesFailed: "Failed to load devices",
        selectGroups: "Select Groups",
        selectGroupsFirst: "Please select groups first",
        selectGroupsPlaceholder: "Please select groups to send",
        selectStatus: "Select Status",
        selectStatusFirst: "Please select status first",
        selectStatusPlaceholder: "Please select device status",
        ruleDescription: "Please select send rules",
        ruleByGroup: "By Group Rule",
        ruleByGroupDesc: "Send according to device groups",
        ruleByStatus: "By Status Rule",
        ruleByStatusDesc: "Send according to device online status",
        noDevicesMatchRule: "No devices match the rule",
        sendToDevicesSuccess: "Successfully sent to {count} devices",
        sendByRuleSuccess: "Rule send successful, sent to {count} devices",
        selectDevicesDialog: "Select Devices",
        selectRulesDialog: "Select Rules",
        confirmSendToDevices: "Confirm sending to selected devices",
      },
    },
    license: {
      serverTab: " Server Authorization",
      terminalTab: " Device Authorization",
      serverLicense: " Server Authorization",
      terminalLicense: " Device Authorization",
      exportServerMachineCode: " Export Server Machine Code",
      importServerLicense: " Import Server License",
      exportTerminalCodes: " Export Device Machine Code",
      exportUnregisteredTerminalCodes: " Export Unregistered Device Machine Code",
      importTerminalCodes: " Import Device License",
      searchDeviceAliasMac: " Search Device Alias or MAC Address",
      deviceAlias: " Device Alias",
      macAddress: " MAC Address",
      registrationStatus: " Registration Status",
      registered: " Registered",
      unregistered: " Unregistered",
      registrationCode: " Registration Code",
      creationTime: " Creation Time",
      onlyDatFiles: " Only .dat files are allowed",
      importingServerLicense: " Importing server license, please wait...",
      serverLicenseImported: " Server license imported successfully",
      serverLicenseImportFailed: " Server license import failed, please check the license file",
      importingTerminalCodes: " Importing device license, please wait...",
      terminalCodesImported: " Device license imported successfully",
      terminalCodesImportFailed: " Device license import failed, please check the license file",
      valid: " Authorized",
      invalid: " Unauthorized",
      expireTime: " Expiration Time",
    },
    menu: {
      accountManagement: "Account",
      templateManagement: "Template",
      publicTemplateManagement: "Public Template",
      materialManagement: "Public Material",
      deviceCenter: "Device",
      resourceManagement: "Resource",
      operationLog: "Operation",
      licenseRegistration: "License Registration",
    },
    upload: {
      onlyVideo: "This option only supports uploading files in video format!",
      onlyImage: "This option only supports uploading files in image format!",
      onlyVideoOrImageAgain: "This option only supports uploading files in video or image format!",
      maxFileCount: "Maximum {count} files can be uploaded!",
      fileTooLarge: "File too large: {file}, maximum size allowed is 1GB",
    },
    resource: {
      form: {
        name: "Name",
        namePlaceholder: "Please enter resource name",
        packName: "Name",
        packNamePlaceholder: "Please enter package name",
        date: "Time",
        mac_address: "MAC Address",
        deviceName: "Device Name",
        deviceNamePlaceholder: "Please enter device name",
        deviceId: "Device ID",
        deviceIdPlaceholder: "Please enter device ID",
        deviceAliasName: "Device Alias",
        resourceNameLabel: "Resource Name: ",
      },
      button: {
        sendByRule: "Send by Rule",
        cancel: "Cancel",
        confirm: "Confirm",
        nextStep: "Next Step",
        sendAll: "Send All",
        sendPart: "Specified send",
      },
      table: {
        num: "Serial No",
        name: "Resource Name",
        pack_name: "Package Name",
        created_at: "Creation Time",
        deleteResource: "Are you sure you want to delete this resource?",
      },
      dialog: {
        title: {
          selectDevice: "Select Device",
          selectResource: "Select Resource",
          inputDevice: "Input Devices for Resource",
          selectGroup: "Select Group",
          group_name: "Group Name",
        },
        tip: {
          noSelectedResources: "No resources selected",
          deviceAliasHint: "Enter device aliases, separate multiple devices with commas",
          confirmSend: "Confirm sending to selected devices. Proceed?",
          selectAtLeastOneDevice: "Please select at least one device!",
          selectAtLeastOneGroup: "Please select at least one group!",
          selectAtLeastOneResource: "Please select at least one resource",
        },
        message: {
          selectedResources: "{count} resources selected",
          selectedGroups: "{count} groups selected",
          sendSuccess: "Sent successfully!",
          sendByRuleSuccess: "Rule sent successfully",
          sendFailed: "Sending failed, please try again later",
          requestError: "Request error, please check your network!",
        },
      },
      confirm: {
        title: "Confirmation",
        sendToDevices: "Confirm sending to selected devices. Proceed?",
        sendToAllDevices: "Are you sure you want to push to all devices?",
      },
      empty: {
        noResources: "No resource packages available"
      },
    },
    commonMaterials: {
      title: "Common Materials",
      searchPlaceholder: "Search materials...",
      ungrouped: "Ungrouped",
      viewMode: {
        list: "List",
        grid: "Grid"
      },
      tabs: {
        all: "All",
        image: "Images",
        video: "Videos"
      },
      empty: "No materials available"
    },
    ...enLocale,
  },
  zh: {
    public: {
      reset: "重置",
      search: "搜索",
      cancel: "取消",
      select: "选择",
      confirm: "确定",
      batchDelete: "批量删除",
      uploadMaterial: "上传文件",
      delete: "删除",
      edit: "编辑",
      add: "新增",
      operation: "操作",
      startDate: "开始日期",
      endDate: "结束日期",
      addSuccess: "新增成功!",
      editSuccess: "编辑成功!",
      deleteSuccess: "删除成功!",
      totalItems: "共 {count} 条",
      to: "至",
    },
    common: {
      reverseMessage: "反转消息"
    },
    login: {
      esopBackend: "ESOP 后台",
      rememberPassword: "记住密码",
      login: "登录",
      enterUsername: "请输入用户名",
      enterPassword: "请输入密码",
      switchLang: "切换到英文",
      loginSuccess: "登录成功",
      loginOut: "退出登录",
    },
    equipmentCenter: {
      actionBar: {
        selectAllOffline: "全选离线",
        selectAllOnline: "全选在线",
        batchUpload: "批量上传",
        batchDeleteFiles: "批量删除文件",
        batchPush: "批量推送",
        batchReboot: "批量重启",
        batchShutdown: "批量关机",
        batchSchedulePower: "批量定时开关机",
        adjustVolume: "调节音量",
        batchUploadTooltip: "批量上传的文件名必须与设备别名匹配；如：sop1-2025-05-08.jpg；sop1为设备别名"
      },
      uploadDialog: {
        title: "向 [{device}] 上传文件",
        dragText: "将文件拖拽到此处，或<span class='click-text'>点击选择文件</span>",
        supportedFormats: "支持图片、视频、PDF格式",
        fileList: "文件列表 ({count})",
        clearAll: "清空全部",
        preview: "预览",
        autoDetectedName: "自动识别名称:",
        totalFiles: "总文件数:",
        totalSize: "总大小:",
        uploaded: "已上传:",
        cancel: "取消",
        uploading: "上传中...",
        startUpload: "开始上传",
        unsupportedFileType: "不支持的文件类型: {file}",
        fileTooLarge: "文件 {file} 过大，最大允许大小为1GB",
        uploadSuccess: "成功上传 {count} 个文件",
        uploadPartialFailure: "{count} 个文件上传失败",
        uploadFailed: "上传失败，请重试",
        fileTypes: {
          image: "图片",
          video: "视频",
          pdf: "PDF",
          ppt: "PPT",
          word: "Word",
          excel: "Excel",
          txt: "TXT",
          other: "其他",
          unknown: "未知"
        }
      },
      table: {
        num: "序号",
        name: "设备名称",
        number: "设备编号",
        status: "设备状态",
        online: "在线",
        offline: "离线",
        model: "设备型号",
        system: "系统",
        system_version: "系统版本",
        serial_number: "序列号",
        screen: "屏幕分辨率",
        alias_name: "设备别名",
        deviceAlias: "设备别名",
        macAddress: "MAC地址",
        connectionStatus: "连接状态",
        openStatus: "打开状态",
        pushStatus: "推送状态",
        group_name: "分组名称",
        created_at: "创建时间",
        updated_at: "最后上线时间",
        ip_addr: "设备IP地址",
        operation: "操作",
        edit: "编辑",
        delete: "删除",
        time: "时间",
        logType: "日志类型",
        logContent: "日志内容",
        sureToDelete: "确定删除该设备?",
      },
      status: {
        offlineStatus: "离线状态",
        idle: "空闲",
        previewing: "预览中",
        downloading: "下载中",
        decompressing: "解压中",
        executingCommand: "正在执行指令",
        uploading: "上传中",
        previewingFile: "正在预览文件"
      },
      dialog: {
        screenshot: "实时截图",
        gettingScreenshot: "正在获取截图...",
        screenshotFailed: "无法加载截图",
        volumeAdjustment: "调节音量",
        powerSchedule: "开关机时间",
        batchDeleteFiles: "批量删除文件",
        selectDeleteDays: "选择删除天数",
        title: {
          add: "新增",
          edit: "编辑",
          addGroup: "新增分组",
          editGroup: "编辑分组",
        },
        form: {
          accountName: "账号名称",
          account: "账号",
          password: "密码",
          accountNamePlaceholder: "请输入账号名称",
          accountPlaceholder: "请输入账号",
          passwordPlaceholder: "请输入密码",
          groupName: "分组名称",
          groupNamePlaceholder: "请输入分组名称",
          groupDescription: "分组描述",
          groupDescriptionPlaceholder: "请输入分组描述",
        },
        message: {
          deleteGroupConfirm: "确定删除该分组吗？",
          deleteGroupWarning: "删除分组将把该分组下的所有设备移动到'无分组'，是否继续？",
          groupNameRequired: "分组名称不能为空",
          groupAddSuccess: "分组新增成功",
          groupEditSuccess: "分组编辑成功",
          groupDeleteSuccess: "分组删除成功",
        },
      },
      message: {
        offlineDevice: "终端离线，无法发送",
        screenshotCommandSent: "已发送截图命令到设备",
        screenshotCommandFailed: "发送截图命令失败",
        otaCommandSent: "已发送OTA升级命令到设备",
        otaCommandFailed: "发送OTA升级命令失败",
        rebootCommandSent: "已发送重启命令到设备",
        rebootCommandFailed: "发送重启命令失败",
        shutdownCommandSent: "已发送关机命令到设备",
        shutdownCommandFailed: "发送关机命令失败",
        batchPushInfo: "准备向 {count} 个设备进行批量推送...",
        batchRebootInfo: "准备向 {count} 个设备发送重启命令...",
        batchShutdownInfo: "准备向 {count} 个设备发送关机命令...",
        batchPushSuccess: "所有 {count} 个设备均已成功发送推送命令。",
        batchRebootSuccess: "所有 {count} 个设备均已成功发送重启命令。",
        batchShutdownSuccess: "所有 {count} 个设备均已成功发送关机命令。",
        batchOperationPartialSuccess: "{success}个设备发送成功, {failure}个设备发送失败",
        batchOperationFailed: "所有设备都未能成功发送。",
        clearLogsSuccess: "清空成功!",
        clearLogsFailed: "清空失败",
        cancelClearLogs: "已取消清空",
        confirmReboot: "确定要远程重启设备: {device}?",
        confirmShutdown: "确定要远程关机设备: {device}? 这可能导致设备离线无法操作。",
        confirmBatchReboot: "确定要批量重启选中的 {count} 个设备吗?",
        confirmBatchShutdown: "确定要批量关机选中的 {count} 个设备吗? 这可能导致设备离线无法操作。",
        cancelBatchReboot: "已取消批量重启操作",
        cancelBatchShutdown: "已取消批量关机操作",
        selectAtLeastOneDevice: "请至少选择一个设备",
        selectGroupFirst: "请先选择一个设备分组",
        invalidDevice: "无效的设备",
        saveSuccess: "保存成功",
        saveFailed: "保存失败",
        deleteSuccess: "删除成功",
        deleteFailed: "删除失败",
        confirmDeleteDevice: "确定删除设备: {device}?",
        confirmDeleteMaterial: "确定要删除素材 \"{name}\" 吗? 这将解除该素材与当前设备的关联。",
        confirmBatchDeleteMaterials: "确定要删除选中的 {count} 个素材吗?",
        batchDeleteSuccess: "批量删除成功",
        batchDeleteFailed: "批量删除失败",
        materialSendSuccess: "素材推送命令已发送",
        materialSendFailed: "素材推送失败",
        disassociateSuccess: "素材删除成功",
        disassociateFailed: "素材删除失败",
        selectAtLeastOneMaterial: "请至少选择一个素材",
        selectMaterialFirst: "请先选择一个素材",
        replaceMaterialSuccess: "素材关联成功",
        replaceMaterialFailed: "素材关联失败",
        confirmReplaceMaterial: "请选择一个素材",
        uploadSuccess: "上传成功",
        uploadFailed: "上传失败",
        uploadError: "上传过程中发生错误，详情请查看控制台。",
        uploadPartialSuccess: "{success}个文件上传成功, {failure}个文件未能匹配到设备。",
        uploadAllSuccess: "所有 {count} 个文件均已成功上传并关联。",
        selectDeleteDays: "请选择删除天数",
        deleteFilesSuccess: "删除成功",
        deleteFilesFailed: "删除失败"
      },
      info: {
        basicInfo: "基本信息",
        materialManagement: "作业指导书管理",
        deviceLogs: "设备日志",
        clearLogs: "清空日志",
        uploadMaterial: "上传文件",
        remoteControl: "远程控制",
        realTimeScreenshot: "实时截图",
        remoteReboot: "远程重启",
        remoteShutdown: "远程关机",
        otaUpgrade: "OTA 升级",
        enterOtaUrl: "请输入OTA升级文件",
        cancelOta: "已取消OTA升级",
        confirmOta: "确定",
        cancel: "取消",
        replaceMaterial: "更换素材",
        volumeAdjustment: "调节音量",
        powerSchedule: "开关机时间",
        batchDeleteFiles: "批量删除文件",
        selectDeleteDays: "选择删除天数",
        addTimeRange: "添加时间段",
        saveSchedule: "保存",
        clearSchedule: "清除定时"
      },
      carousel: {
        debugInfo: "轮播: {clientKey} | 索引: {currentIndex} | 文件数: {totalFiles}"
      },
      tree: {
        null: "无分组",
        title: "设备列表",
        searchPlaceholder: "输入关键字进行过滤",
        selectTip: "请选择左侧设备查看详情",
        basicInfo: "设备信息",
        total: "设备总数",
        online: "在线",
        offline: "离线",
        noData: "暂无设备数据",
      },
      material: {
        title: "素材与预览",
        noMaterial: "当前设备未关联素材"
      },
      form: {
        name: "设备名称",
        aliasName: "设备别名",
        number: "设备编号",
        mac: "MAC地址",
        date: "创建时间",
        status: "设备状态",
        namePlaceholder: "请输入设备名称",
        numberPlaceholder: "请输入设备编号",
        groupPlaceholder: "请选择设备分组",
        macPlaceholder: "请输入设备MAC地址",
      },
      button: {
        deviceEdit: "设备编辑",
        allSend: "全部",
        create: "创建",
        cancel: "取消",
        edit: "编辑",
        delete: "删除",
        editTemplate: "模板编辑",
        singleSend: "单个发送",
        upload: "上传文件",
        replace: "更换素材",
        preview: "实时预览",
        addGroup: "新增分组",
        editGroup: "编辑分组",
        deleteGroup: "删除分组"
      },
    },
    operationLog: {
      form: {
        operatorName: "操作人名称",
        operatorAccount: "操作人账号",
        operationTime: "操作时间",
        operatorNamePlaceholder: "请输入操作人名称",
        operatorAccountPlaceholder: "请输入操作人账号",
      },
      table: {
        num: "序号",
        operatorName: "操作人名称",
        operatorAccount: "操作人账号",
        action: "类型",
        module: "模块",
        operationTime: "操作时间",
      },
    },
    material: {
      group: {
        title: "素材分组",
        all: "全部分组"
      },
      groupDialog: {
        title: "分组管理",
        enterGroupName: "请输入分组名称",
        groupDescription: "分组描述（可选）",
        addGroup: "新增分组",
        existingGroups: "现有分组",
        refresh: "刷新",
        noGroups: "暂无分组，请先创建分组",
        createTime: "创建时间:",
        edit: "编辑",
        delete: "删除",
        close: "关闭",
        editGroup: "编辑分组",
        groupName: "分组名称",
        groupDescriptionLabel: "分组描述",
        confirm: "确定",
        cancel: "取消",
        enterGroupNameRequired: "请输入分组名称",
        groupNameLengthError: "分组名称长度在 1 到 50 个字符",
        groupDescriptionLengthError: "分组描述不能超过 200 个字符",
        loadGroupsFailed: "获取分组列表失败",
        groupCreatedSuccess: "分组创建成功",
        createGroupFailed: "创建分组失败",
        groupUpdatedSuccess: "分组更新成功",
        updateGroupFailed: "更新分组失败",
        confirmDeleteGroup: "确定要删除分组\"{name}\"吗？",
        confirmTitle: "提示",
        groupDeletedSuccess: "分组删除成功",
        deleteGroupFailed: "删除分组失败"
      },
      button: {
        upload: "上传文件",
        add: "新增素材",
        selectAllCurrent: "全选当前页",
        batchDelete: "批量删除"
      },
      uploadDialog: {
        title: "上传文件",
        selectGroupPlaceholder: "请选择分组",
        ungrouped: "未分组",
        addNewGroup: "新增分组",
        dragText: "将文件拖拽到此处，或<span class='click-text'>点击选择文件</span>",
        supportedFormats: "支持图片、视频、PDF格式",
        fileList: "文件列表 ({count})",
        clearAll: "清空全部",
        preview: "预览",
        autoDetectedName: "自动识别名称:",
        totalFiles: "总文件数:",
        totalSize: "总大小:",
        uploaded: "已上传:",
        cancel: "取消",
        uploading: "上传中...",
        startUpload: "开始上传",
        unsupportedFileType: "不支持的文件类型: {file}",
        fileTooLarge: "文件 {file} 过大，最大允许大小为1GB",
        uploadSuccess: "成功上传 {count} 个文件",
        uploadPartialFailure: "{count} 个文件上传失败",
        uploadFailed: "上传失败，请重试",
        enterGroupName: "请输入分组名称",
        addGroup: "新增分组",
        confirm: "确定",
        cancelDialog: "取消",
        groupNameLengthError: "分组名称长度在 1 到 50 个字符",
        groupCreatedSuccess: "分组创建成功",
        createGroupFailed: "创建分组失败",
        fileTypes: {
          image: "图片",
          video: "视频",
          pdf: "PDF",
          ppt: "PPT",
          word: "Word",
          excel: "Excel",
          txt: "TXT",
          other: "其他",
          unknown: "未知"
        }
      },
      form: {
        name: "素材名称",
        group: "所属分组",
        groupPlaceholder: "请选择分组",
        noGroup: "不分组",
        date: "创建时间",
        namePlaceholder: "请输入素材名称",
      },
      table: {
        num: "序号",
        name: "素材名称",
        type: "素材类型",
        preview: "素材预览",
        created_at: "创建时间",
        operation: "操作",
        sureToDelete: "确定删除该素材?",
        image: "图片",
        video: "视频",
        file: "文件",
        deviceAlias: "设备别名",
        macAddress: "MAC地址",
        connectionStatus: "连接状态",
        pushStatus: "下发状态"
      },
      status: {
        online: "在线",
        offline: "离线"
      },
      confirm: {
        delete: "确定要删除这个素材吗?",
        batchDelete: "确定要删除选中的 {count} 个素材吗?"
      },
      empty: {
        noMaterial: "暂无素材"
      },
      dialog: {
        title: {
          addMaterial: "新增素材",
          editMaterial: "编辑素材",
        },
        replaceMaterial: "更换素材",
        editMaterial: "编辑素材",
        form: {
          name: "素材名称",
          type: "素材类型",
          path: "素材路径",
          upload: "上传文件",
          namePlaceholder: "请输入素材名称",
          typePlaceholder: "请选择素材类型",
          pathPlaceholder: "请上传文件",
          selectFile: "请上传文件",
          onlyImage: "请上传图片类型的素材",
          onlyVideo: "请上传视频类型的素材",
          onlyPDF: "请上传PDF类型的文件",
          onlyFile: "请上传支持的文件类型（PDF, Word, Excel, PowerPoint）",
          materialName: "素材名称",
          group: "所属分组",
          groupPlaceholder: "请选择分组",
          noGroup: "不分组"
        },
      },
      message: {
        deleteSuccess: "删除成功",
        batchDeleteSuccess: "批量删除成功",
        editSuccess: "编辑成功",
        sendSuccess: "素材推送命令已发送"
      },
      error: {
        deleteFailed: "删除失败",
        batchDeleteFailed: "批量删除失败",
        editFailed: "编辑失败",
        sendFailed: "素材推送失败"
      }
    },
    account: {
      form: {
        name: "账号名称",
        account: "账号",
        password: "密码",
        createTime: "创建时间",
        namePlaceholder: "请输入账号名称",
        accountPlaceholder: "请输入账号",
        passwordPlaceholder: "请输入密码",
      },
      button: {
        addAccount: "新建账号",
      },
      table: {
        num: "序号",
        name: "账号名称",
        account: "账号",
        created_at: "创建时间",
        confirmDelete: "这是一段内容确定删除吗？",
      },
      dialog: {
        title: {
          add: "新增账号",
          edit: "编辑账号",
        },
      },
    },
    upload: {
      onlyVideo: "该选项只支持视频格式的文件",
      onlyImage: "该选项只支持上传图片格式的文件！",
      onlyVideoOrImageAgain: "该选项只支持上传视频或图片格式的文件！",
      maxFileCount: "最多上传 {count} 个文件！",
      fileTooLarge: "文件 {file} 过大，最大允许大小为1GB",
    },
    template: {
      background: {
        repeat: "平铺",
        cover: "拉伸",
        contain: "适应",
        auto: "原始大小",
      },
      propertiesPanel: {
        templateProperties: "模板属性",
        templateName: "模板名称",
        templateNamePlaceholder: "请输入模板名称",
        resolution: "分辨率",
        resolutionPlaceholder: "请选择分辨率",
        areaProperties: "区域属性",
        left: "左",
        top: "上",
        width: "宽",
        height: "高",
        currentAreaMaterial: "当前区域素材",
        sort: "排序",
        time: "时间",
        file: "文件",
        operation: "操作",
        delete: "删除",
        layerOrder: "图层顺序",
        noMaterialOnPage: "当前页面没有素材",
        timeComponent: "时间组件",
        webComponent: "网页组件",
        unnamedMaterial: "未命名素材",
        filesCount: "{count} 个文件",
        emptyArea: "空区域",
        textMarqueeProperties: "文本跑马灯属性",
        marqueeText: "滚动文本",
        fontSize: "字体大小",
        scrollSpeed: "滚动速度",
        fontColor: "字体颜色",
        backgroundColor: "背景颜色"
      },
      form: {
        name: "模板名称",
        date: "创建时间",
        namePlaceholder: "请输入模板名称",
        urlPlaceholder: "请输入url路径",
        swipterTime: "定时切换时间",
        resolutionRatio: "分辨率",
        resolutionRatioPlaceholder: "请选择分辨率",
        resourcePackName: "资源名称",
        resourcePackAlias: "资源别名",
        resourcePackAliasPlaceholder: "请输入资源包别名",
        successTips: "打包成功",
        resourcePackNamePlaceholder: "请输入资源包名称",
        materialsPlaceholder: "请选择素材",
        iframeUrl: "嵌入网页地址",
      },
      button: {
        create: "新建模板",
        updateWorkTemplate: "修改作业模板",
        createWorkTemplate: "新建作业模板",
        addMaterial: "添加素材",
        clearBackground: "清空背景图",
        clearTemplate: "重置",
        prevPage: "上一页",
        nextPage: "下一页",
        addNewPage: "新增一页",
        setBackground: "设置背景图",
        addImage: "添加图片",
        addVideo: "添加视频",
        dateTime: "日期时间",
        iframe: "嵌入网页",
        page: "页",
        delPage: "删除页面",
        addNewPageShort: "新增页面",
        delPageShort: "删除页面",
        resetCurrentPage: "重置当前页",
        edit: "编辑",
        send: "推送模版",
        delete: "删除",
        addTextMarquee: "添加文本跑马灯",
        textMarquee: "文本跑马灯"
      },
      table: {
        num: "序号",
        name: "模板名称",
        type: "模板类型",
        created_at: "创建时间",
        operation: "操作",
        sureToDelete: "确定删除该模板?",
      },
      dialog: {
        title: {
          add: "新增模板",
          edit: "编辑模板",
          pack: "打包",
          material: "选择素材",
          selectImageMulti: "选择图片（支持多选）",
          selectVideoMulti: "选择视频（支持多选）",
          addMaterial: "添加素材",
          iframeDialog: "iframe填写弹窗",
          packDialog: "打包弹窗",
          sendDialog: "发送弹窗",
        },
        pageInfo: {
          currentPage: "当前第",
          totalPages: "页，共",
          pageDisplay: "第 {current} / {total} 页",
        },
        materialType: {
          image: "图片",
          video: "视频",
          file: "文件",
          dateTime: "日期时间",
        },
      },
      search: {
        placeholder: "按名称搜索",
      },
      empty: {
        noTemplates: "暂无模板",
      },
      group: {
        title: "素材分组",
        all: "全部分组",
      },
      confirm: {
        deleteTemplate: "确定要删除这个模板吗?",
        deleteMaterial: "确定要删除这个素材吗?",
      },
      message: {
        materialAdded: "素材已添加",
        dateTimeAdded: "时间组件添加成功",
        materialPlaced: "素材已成功放置到{areaType}中",
        materialNotMatch: "素材不能放置到{areaType}中，请选择匹配的素材类型",
        materialReplaced: "素材已替换当前区域内容",
        materialAddedToCarousel: "素材已添加到{areaType}轮播（共{count}个文件）",
        materialTypeMismatch: "素材类型与区域类型不匹配，无法替换",
        materialDropError: "放置素材时发生错误",
        layerUpdated: "素材层级更新成功",
        deleteMaterialFailed: "删除素材失败",
        materialNotFound: "素材未找到",
        dragStart: "开始拖拽区域",
        resizeStart: "开始调整区域大小",
        unknownMaterial: "未知素材",
        timeComponent: "时间组件",
        webComponent: "网页组件",
        unnamedMaterial: "未命名素材",
        selectedMaterials: "已选择 {count} 个{type}",
        confirmAdd: "确认添加 ({count})",
        materialInfo: "素材：{name}",
        image: "图片",
        pixels: "px",
        textMarqueeAdded: "文本跑马灯已添加"
      },
      tooltip: {
        addImageArea: "点击添加图片区域",
        addVideoArea: "点击添加视频区域",
        addTimeComponent: "点击添加时间组件",
        addWebArea: "点击添加网页区域",
      },
      select: {
        backgroundSize: "背景显示方式",
        backgroundRepeat: "重复",
        backgroundCover: "覆盖",
        backgroundContain: "包含",
        backgroundAuto: "自动",
      },
      type: {
        normalTemplate: "普通模板",
        workTemplate: "工作模板",
      },
      send: {
        description: "请选择发送方式",
        allDevices: "全部发送",
        allDevicesDesc: "将模板发送到所有在线设备",
        specificDevices: "指定发送",
        specificDevicesDesc: "选择特定设备进行发送",
        byRules: "按规则发送",
        byRulesDesc: "根据预设规则自动发送",
        allDevicesSuccess: "已成功发送到所有设备",
        selectDevicesFirst: "请先选择要发送的设备",
        selectRulesFirst: "请先选择发送规则",
        sendFailed: "发送失败",
        selectDevices: "选择设备",
        selectRules: "选择规则",
        confirmSend: "确认发送",
        confirmSendByRule: "按规则确认发送",
        loadGroupsFailed: "获取分组列表失败",
        loadDevicesFailed: "获取设备列表失败",
        selectGroups: "选择分组",
        selectGroupsFirst: "请先选择分组",
        selectGroupsPlaceholder: "请选择要发送的分组",
        selectStatus: "选择状态",
        selectStatusFirst: "请先选择状态",
        selectStatusPlaceholder: "请选择设备状态",
        ruleDescription: "请选择发送规则",
        ruleByGroup: "按分组规则",
        ruleByGroupDesc: "根据设备分组进行发送",
        ruleByStatus: "按状态规则",
        ruleByStatusDesc: "根据设备在线状态进行发送",
        noDevicesMatchRule: "没有符合规则的设备",
        sendToDevicesSuccess: "成功发送到 {count} 个设备",
        sendByRuleSuccess: "按规则发送成功，共发送到 {count} 个设备",
        selectDevicesDialog: "选择设备",
        selectRulesDialog: "选择规则",
        confirmSendToDevices: "确认发送到选中的设备",
      },
    },
    license: {
      serverTab: "服务器授权",
      terminalTab: "终端设备授权",
      serverLicense: "服务器授权",
      terminalLicense: "终端授权",
      exportServerMachineCode: "导出服务器机器码",
      importServerLicense: "导入服务器注册码",
      exportTerminalCodes: "导出终端机器码",
      exportUnregisteredTerminalCodes: "导出未注册终端机器码",
      importTerminalCodes: "导入终端注册码",
      searchDeviceAliasMac: "搜索设备别名/MAC",
      deviceAlias: "设备别名",
      macAddress: "MAC地址",
      registrationStatus: "注册状态",
      registered: "已注册",
      unregistered: "未注册",
      registrationCode: "注册码",
      creationTime: "创建时间",
      onlyDatFiles: "只能上传 .dat 格式的文件",
      importingServerLicense: "正在导入服务器注册码，请稍候...",
      serverLicenseImported: "服务器注册码导入成功",
      serverLicenseImportFailed: "服务器注册码导入失败,请检查授权码是否正确",
      importingTerminalCodes: "正在导入终端注册码，请稍候...",
      terminalCodesImported: "终端注册码导入成功",
      terminalCodesImportFailed: "终端注册码导入失败,请检查授权码是否正确",
      valid: "已授权",
      invalid: "未授权",
      expireTime: "过期时间",
    },
    menu: {
      accountManagement: "账号管理",
      templateManagement: "模板管理",
      publicTemplateManagement: "公共模板管理",
      materialManagement: "公共素材管理",
      deviceCenter: "设备中心",
      resourceManagement: "资源管理",
      operationLog: "操作日志",
      licenseRegistration: "注册授权",
    },
    resource: {
      form: {
        name: "资源名称",
        namePlaceholder: "请输入资源名称",
        packName: "包名称",
        packNamePlaceholder: "请输入包名称",
        date: "创建时间",
        mac_address: "MAC地址",
        deviceName: "设备名称",
        deviceNamePlaceholder: "请输入设备名称",
        deviceId: "设备编号",
        deviceIdPlaceholder: "请输入设备编号",
        deviceAliasName: "设备别名",
        resourceNameLabel: "资源名称 ：",
      },
      button: {
        sendByRule: "按规则发送",
        sendAll: "全部发送",
        sendPart: "指定发送",
        cancel: "取消",
        confirm: "确定",
        nextStep: "下一步",
      },
      table: {
        num: "序号",
        name: "资源名称",
        pack_name: "包名称",
        created_at: "创建时间",
        deleteResource: "确定删除该资源？",
      },
      dialog: {
        title: {
          selectDevice: "选择设备",
          selectResource: "选择资源",
          inputDevice: "输入资源对应的设备",
          selectGroup: "选择分组",
          group_name: "分组名称",
        },
        tip: {
          noSelectedResources: "暂无选中资源",
          deviceAliasHint: "请输入设备别名，多个设备请使用逗号分隔",
          confirmSend: "请确定是否发送到已选中的设备, 是否继续?",
          selectAtLeastOneDevice: "请至少选择一个设备！",
          selectAtLeastOneGroup: "请至少选择一个分组！",
          selectAtLeastOneResource: "请至少选择一个资源!",
        },
        message: {
          selectedResources: "已选中 {count} 条资源数据",
          selectedGroups: "已选中 {count} 条分组数据",
          sendSuccess: "发送成功!",
          sendByRuleSuccess: "规则发送成功！",
          sendFailed: "发送失败，请稍后重试！",
          requestError: "请求出错，请检查网络！",
        },
      },
      confirm: {
        title: "提示",
        sendToDevices: "请确定是否发送到已选中的设备, 是否继续?",
        sendToAllDevices: "请确定是否推送到所有设备？",
      },
      empty: {
        noResources: "暂无资源包"
      },
    },
    commonMaterials: {
      title: "公共素材",
      searchPlaceholder: "搜索素材...",
      ungrouped: "未分组",
      viewMode: {
        list: "列表",
        grid: "缩略图"
      },
      tabs: {
        all: "全部",
        image: "图片",
        video: "视频"
      },
      empty: "暂无素材"
    },
    ...zhLocale,
  },
};

const savedLanguage = localStorage.getItem('appLanguage') || 'zh';

const i18n = new VueI18n({
  locale: savedLanguage,
  messages,
});

locale.i18n((key, value) => i18n.t(key, value));
export default i18n;
