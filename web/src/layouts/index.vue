<template>
  <div id="app">
    <el-container style="height: 100vh; border: 1px solid #eee">
      <el-aside width="200px" style="background-color: #001529">
        <div class="projrct-name">{{ $t('login.esopBackend') }}</div>
        <el-menu :default-active="activeMenuIndex" background-color="#001529" text-color="#fff"
          active-text-color="#409eff">
          <router-link :to="item.path" v-for="(item, index) in menuItems" :key="index">
            <el-menu-item :index="item.index">
              <i :class="item.icon"></i>
              <span slot="title">{{ item.title }}</span>
            </el-menu-item>
          </router-link>
        </el-menu>
      </el-aside>

      <el-container>
        <el-header style="text-align: right; font-size: 12px">
          <el-dropdown class="user-container" trigger="hover" style="cursor: pointer">
            <div class="user right-menu-item hover-effect">
              <div class="flex">
                <i class="el-icon-user-solid" />
                <span>{{ name }}</span>
              </div>
            </div>
            <el-dropdown-menu slot="dropdown" class="user-dropdown">
              <el-dropdown-item @click.native="logout">
                <span style="display: block">{{ $t('login.loginOut') }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-header>

        <el-main>
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
export default {
  name: "App",
  data () {
    return {
      isLogin: false,
      menuItems: [

        {
          title: this.$i18n.t("menu.deviceCenter"),
          icon: "el-icon-s-platform",
          index: "1",
          path: "/EquipmentCenter",
        },
        {
          title: this.$i18n.t("menu.publicTemplateManagement"),
          icon: "el-icon-s-order",
          index: "2",
          path: "/Template",
        },
        {
          title: this.$i18n.t("menu.materialManagement"),
          icon: "el-icon-picture",
          index: "3",
          path: "/Material",
        },

        {
          title: this.$i18n.t("menu.accountManagement"),
          icon: "el-icon-s-custom",
          index: "5",
          path: "/Account",
        },
        {
          title: this.$i18n.t("menu.operationLog"),
          icon: "el-icon-s-check",
          index: "6",
          path: "/OperationLog",
        },
        {
          title: this.$i18n.t("menu.licenseRegistration"),
          icon: 'el-icon-key',
          index: '7',
          path: '/License',
        },
      ],
      name: JSON.parse(localStorage.getItem("greemall_login")).name,
    };
  },
  computed: {
    activeMenuIndex () {
      return this.menuItems.find(item => item.path === this.$route.path)?.index || "1";

    }
  },
  methods: {
    async logout () {
      await this.$store.dispatch("user/logout");
      this.$store.commit("tagsView/SET_RESET_VIES");
      this.$router.push(`/login`);
    },
  },
};
</script>

<style>
body {
  padding: 0;
  margin: 0;
}

.el-menu a {
  text-decoration: none;
}

#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
}

.projrct-name {
  height: 60px;
  text-align: center;
  line-height: 60px;
  font-size: 26px;
  font-weight: bold;
  color: #fff;
}

.el-header {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  border-bottom: 2px solid #eee;
}

.el-aside {
  color: #333;
}
</style>
