<template>
    <div class="common-materials-panel">
        <div class="panel-header">
            <h4>{{ $t('commonMaterials.title') }}</h4>
            <div class="header-controls">
                <el-input v-model="searchTerm" :placeholder="$t('commonMaterials.searchPlaceholder')" size="mini"
                    clearable @input="filterMaterials" class="search-input">
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>
            </div>
        </div>

        <div class="material-tree-container" v-loading="isLoading">
            <el-tree :data="materialTree" :props="treeProps" node-key="id" default-expand-all
                :filter-node-method="filterNode" ref="tree">
                <span class="custom-tree-node" slot-scope="{ node, data }" draggable="true"
                    @dragstart="handleDragStart($event, data)">
                    <span v-if="!data.children">
                        <i :class="getIconClass(data)"></i>
                        <span class="node-label">{{ node.label }}</span>
                        <span class="file-extension">{{ getFileExtension(data.path) }}</span>
                    </span>
                    <span v-else>
                        <i :class="getIconClass(data)"></i>
                        <span class="node-label">{{ node.label }}</span>
                    </span>
                </span>
            </el-tree>
            <el-empty v-if="!isLoading && materialTree.length === 0" :description="$t('commonMaterials.empty')"
                :image-size="80"></el-empty>
        </div>
    </div>
</template>

<script>
import { getMaterialTree } from "@/api/template.js";

export default {
    name: "CommonMaterialsPanel",
    data () {
        return {
            isLoading: false,
            searchTerm: "",
            materialTree: [],
            treeProps: {
                children: 'children',
                label: 'name'
            },
            imageUrl: process.env.VUE_APP_BASE_API + "assets/media/",
        };
    },
    created () {
        this.fetchData();
    },
    methods: {
        async fetchData () {
            this.isLoading = true;
            try {
                const res = await getMaterialTree();
                if (res.code === 0) {
                    this.materialTree = res.data || [];
                }
            } catch (error) {
                console.error("Error fetching material tree:", error);
                this.$message.error('素材加载失败');
            } finally {
                this.isLoading = false;
            }
        },
        filterNode (value, data) {
            if (!value) return true;
            if (data.children) { // It's a group
                return data.name.toLowerCase().includes(value.toLowerCase()) ||
                    data.children.some(child => child.name.toLowerCase().includes(value.toLowerCase()));
            }
            // It's a material
            return data.name.toLowerCase().includes(value.toLowerCase());
        },
        handleDragStart (event, material) {
            if (material.children) { // Prevent dragging groups
                event.preventDefault();
                return;
            }
            event.dataTransfer.setData("application/json", JSON.stringify(material));
            event.dataTransfer.effectAllowed = "copy";
        },
        getIconClass (data) {
            if (data.children) {
                return 'el-icon-folder';
            }
            if (data.type === 1) {
                return 'el-icon-picture-outline';
            } else if (data.type === 2) {
                return 'el-icon-video-camera';
            }
            return 'el-icon-document';
        },
        getFileExtension (filePath) {
            if (!filePath) return '';
            const extension = filePath.split('.').pop().toLowerCase();
            return extension ? `.${extension}` : '';
        },
        filterMaterials (val) {
            this.$refs.tree.filter(val);
        }
    },
};
</script>

<style scoped lang="scss">
.common-materials-panel {
    width: 100%;
    height: 720px;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-right: 1px solid #e0e0e0;
    padding: 10px;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.panel-header {
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;

    h4 {
        margin: 0 0 8px 0;
        font-size: 13px;
        font-weight: 600;
        color: #303133;
    }

    .header-controls {
        .search-input {
            width: 100%;
        }
    }
}

.material-tree-container {
    flex: 1;
    overflow-y: auto;
}

.custom-tree-node {
    display: flex;
    align-items: center;
    font-size: 12px;
    width: 100%;
    cursor: grab;

    .node-label {
        margin-left: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .file-extension {
        margin-left: 4px;
        color: #909399;
        font-size: 10px;
        flex-shrink: 0;
    }
}

.thumbnail-preview {
    width: 200px;
    height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;

    .el-image,
    video {
        max-width: 100%;
        max-height: 100%;
    }
}

:deep(.el-tree-node__content) {
    padding: 5px 0;
}
</style>