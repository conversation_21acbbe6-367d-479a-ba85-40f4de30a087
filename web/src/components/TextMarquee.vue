<template>
  <div class="text-marquee-container" :style="containerStyle">
    <div class="marquee-content" :style="contentStyle" ref="marqueeContent">
      {{ text }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'TextMarquee',
  props: {
    text: {
      type: String,
      default: ''
    },
    fontSize: {
      type: Number,
      default: 24
    },
    scrollSpeed: {
      type: Number,
      default: 50
    },
    fontColor: {
      type: String,
      default: '#000000'
    },
    backgroundColor: {
      type: String,
      default: '#ffffff'
    },
    width: {
      type: Number,
      default: 400
    },
    height: {
      type: Number,
      default: 60
    }
  },
  data () {
    return {
      animationId: null,
      currentPosition: 0,
      textWidth: 0,
      containerWidth: 0
    };
  },
  computed: {
    containerStyle () {
      return {
        width: this.width + 'px',
        height: this.height + 'px',
        backgroundColor: this.backgroundColor,
        overflow: 'hidden',
        position: 'relative',
        display: 'flex',
        alignItems: 'center'
      };
    },
    contentStyle () {
      return {
        fontSize: this.fontSize + 'px',
        color: this.fontColor,
        whiteSpace: 'nowrap',
        position: 'absolute',
        left: this.currentPosition + 'px',
        lineHeight: this.height + 'px',
        transition: 'none'
      };
    },
    animationSpeed () {
      // 将速度值转换为实际的像素/秒
      return this.scrollSpeed;
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initAnimation();
    });
  },
  beforeDestroy () {
    this.stopAnimation();
  },
  watch: {
    text () {
      console.log('TextMarquee: text changed to', this.text);
      this.$nextTick(() => {
        this.initAnimation();
      });
    },
    fontSize () {
      console.log('TextMarquee: fontSize changed to', this.fontSize);
      this.initAnimation();
    },
    scrollSpeed () {
      console.log('TextMarquee: scrollSpeed changed to', this.scrollSpeed);
      this.initAnimation();
    },
    fontColor () {
      console.log('TextMarquee: fontColor changed to', this.fontColor);
    },
    backgroundColor () {
      console.log('TextMarquee: backgroundColor changed to', this.backgroundColor);
    },
    width () {
      console.log('TextMarquee: width changed to', this.width);
      this.$nextTick(() => {
        this.initAnimation();
      });
    },
    height () {
      console.log('TextMarquee: height changed to', this.height);
      this.$nextTick(() => {
        this.initAnimation();
      });
    }
  },
  methods: {
    initAnimation () {
      this.stopAnimation();
      this.measureText();
      this.startAnimation();
    },
    measureText () {
      if (this.$refs.marqueeContent) {
        this.textWidth = this.$refs.marqueeContent.scrollWidth;
        this.containerWidth = this.width;
        // 从右侧开始
        this.currentPosition = this.containerWidth;
      }
    },
    startAnimation () {
      if (this.textWidth <= this.containerWidth) {
        // 如果文本宽度小于容器宽度，居中显示
        this.currentPosition = (this.containerWidth - this.textWidth) / 2;
        return;
      }

      let lastTime = performance.now();

      const animate = (currentTime) => {
        const deltaTime = (currentTime - lastTime) / 1000; // 转换为秒
        lastTime = currentTime;

        // 根据速度计算移动距离
        const moveDistance = this.animationSpeed * deltaTime;
        this.currentPosition -= moveDistance;

        // 当文本完全移出左侧时，重新从右侧开始
        if (this.currentPosition < -this.textWidth) {
          this.currentPosition = this.containerWidth;
        }

        this.animationId = requestAnimationFrame(animate);
      };

      this.animationId = requestAnimationFrame(animate);
    },
    stopAnimation () {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId);
        this.animationId = null;
      }
    }
  }
};
</script>

<style scoped>
.text-marquee-container {
  border: 1px solid #ddd;
  box-sizing: border-box;
}

.marquee-content {
  font-family: Arial, sans-serif;
  user-select: none;
}
</style>
