export function zipPack (title, page, swipter_time, pageSize, templateType, canvasSize) {
    if (templateType === 2) {
        return "";
    }

    const [pageWidth, pageHeight] = pageSize.split('x').map(Number);
    const [canvasWidth, canvasHeight] = canvasSize.split('x').map(Number);
    const swipterTime = swipter_time;
    const length = Object.keys(page).length;
    console.log(pageWidth, pageHeight, canvasWidth, canvasHeight,);

    let boardWidth = 0;
    for (const key in page) {
        page[key].forEach(element => {
            if (element.template_sm_type !== 3) { // Ignore background for scaling calculations
                boardWidth = Math.max(boardWidth, element.x_axis + element.width);
            }
        });
    }

    if (boardWidth === 0) boardWidth = pageWidth;
    const scale = pageWidth / canvasWidth;

    let slideItem = "";
    let timeHtml = "";
    let swiperButtonOpacity = length > 1 ? 0.3 : 0;

    for (let item in page) {
        let backgroundUrl = "";
        let backgroundSize = "";
        let backgroundRepeat = "";
        let imgHtml = "";
        let pageContent = page[item];
        imgHtml = ""; // Reset for each page

        pageContent.forEach((element) => {
            backgroundSize = element.background_display || "cover";
            backgroundRepeat = backgroundSize === "repeat" ? "repeat" : "no-repeat";

            const width = element.width * scale;
            const height = element.height * scale;
            const top = element.y_axis * scale;
            const left = element.x_axis * scale;
            let style = "";
            if (element.template_sm_type === 2) {
                style = `width: ${element.width}px; height: ${element.height}px; top: ${top}px; left: ${left}px; z-index: ${element.template_index};`;
            } else {
                style = `width: ${width}px; height: ${height}px; top: ${top}px; left: ${left}px; z-index: ${element.template_index};`;
            }


            if (element.template_sm_type === 1) { // Component Area
                if (element.type === 1) { // Image Type
                    if (element.multiFiles && element.multiFiles.length > 1) {
                        const carouselId = `carousel-${element.sm_id || Math.random().toString(36).substring(7)}`;
                        let slides = element.multiFiles.map(file => {
                            const slideDurationAttr = `data-swiper-autoplay="${file.interval_time * 1000}"`;
                            return `<div class="swiper-slide" ${slideDurationAttr}><img src="./assets/${file.path}" style="width:${element.width * scale}px;height:${element.height * scale}px; alt=""></div>`;
                        }).join('');
                        imgHtml += `<div id="${carouselId}" class="swiper-container" style="${style}"><div class="swiper-wrapper">${slides}</div></div>`;
                    } else {
                        imgHtml += `<img class="img" src="./assets/${element.path}" style="${style}" alt="">`;
                    }
                } else if (element.type === 2) { // Video Type
                    if (element.multiFiles && element.multiFiles.length > 1) {
                        const videoPlaylistId = `video-playlist-${element.sm_id || Math.random().toString(36).substring(7)}`;
                        const filesData = JSON.stringify(element.multiFiles.map(f => `./assets/${f.path}`));
                        imgHtml += `
                            <div class="video-playlist-container" id="${videoPlaylistId}" data-files='${filesData}' style="${style}">
                                <video style="width:100%;height:100%;" autoplay muted playsinline preload="auto"></video>
                            </div>
                        `;
                    } else {
                        imgHtml += `<video class="video" style="${style}" autoplay muted playsinline preload="auto" loop>
                                      <source src="./assets/${element.path}" type="video/${element.path.split(".").pop()}">
                                    </video>`;
                    }
                }
            } else if (element.template_sm_type === 2) { // DateTime
                imgHtml += `<div id="datetime" style="${style} color: #000; background-color: #fff; padding: 11px 4px; border-radius: 4px; font-size: 18px; font-weight: bold; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1);"></div>`;
                timeHtml = "updateDateTime(); setInterval(updateDateTime, 1000);";
            } else if (element.template_sm_type === 3) { // Background
                backgroundUrl = element.path;
            } else if (element.template_sm_type === 5) { // Iframe
                imgHtml += `<iframe class="img" src="${element.url}" style="${style}" frameborder="0" sandbox="allow-scripts allow-same-origin"></iframe>`;
            }
        });

        slideItem += `
            <div class="warp-swiper-slide" style="background-image: url(${backgroundUrl ? `./assets/${backgroundUrl}` : ''}); background-size: ${backgroundSize}; background-repeat: ${backgroundRepeat}; background-position: center center;">
                ${imgHtml}
            </div>
        `;
    }

    let html_content = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <link rel="stylesheet" href="./assets/swiper.min.css">
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            html, body { height: 100%; }
            .swiper-container { width: ${pageWidth}px; height: ${pageHeight}px; overflow: hidden; }
            .swiper-slide { position: relative; width: ${pageWidth}px; height: ${pageHeight}px; }
            .warp-swiper-slide { position: relative; width: ${pageWidth}px; height: ${pageHeight}px; }
            .img, .video, .swiper-container, .video-playlist-container { position: absolute; }
            .video { background-color: #000; }
            .swiper-button-next, .swiper-button-prev { color: white; opacity: ${swiperButtonOpacity}; }
        </style>
    </head>
    <body>
        <div class="swiper-container">
            <div class="swiper-wrapper">
                ${slideItem}
            </div>
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>
        <script src="./assets/jquery.min.js"></script>
        <script src="./assets/swiper.min.js"></script>
        <script>
            function updateDateTime() {
                const now = new Date();
                const options = { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' };
                const formattedDateTime = now.toLocaleDateString(undefined, options);
                document.querySelectorAll('#datetime').forEach(el => el.textContent = formattedDateTime);
            }
            ${timeHtml}
            document.addEventListener('DOMContentLoaded', () => {
                var mainSwiper = new Swiper('.swiper-container', {
                    direction: 'horizontal',
                    loop: ${length > 1},
                    ${swipterTime > 0 ? `autoplay: { delay: ${swipterTime * 1000}, disableOnInteraction: false },` : ''}
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                });

                document.querySelectorAll('.swiper-container[id^="carousel-"]').forEach(el => {
                    new Swiper(el, {
                        loop: true,
                        autoplay: true,
                    });
                });

                document.querySelectorAll('.video-playlist-container').forEach(container => {
                    const videoElement = container.querySelector('video');
                    const files = JSON.parse(container.dataset.files);
                    let currentIndex = 0;

                    if (!files || files.length === 0) return;

                    function playNextVideo() {
                        if (currentIndex >= files.length) {
                            currentIndex = 0; // Loop back to the first video
                        }
                        videoElement.src = files[currentIndex];
                        videoElement.load();
                        videoElement.play();
                        currentIndex++;
                    }

                    videoElement.addEventListener('ended', playNextVideo);

                    // Start playing the first video
                    playNextVideo();
                });
            const unmuteHandler = () => {
                document.querySelectorAll('video').forEach(video => {
                    video.muted = false;
                });
                document.removeEventListener('click', unmuteHandler);
                document.removeEventListener('touchend', unmuteHandler);
            };
            document.addEventListener('click', unmuteHandler);
            document.addEventListener('touchend', unmuteHandler);
        });
        </script>
    </body>
    </html>`;
    return html_content;
}