import request, { postBlob, getBlob, handleImport } from '@/utils/request'

export function getList (params) {
  return request({
    url: `/admin/resourcepack/getList`,
    method: 'get',
    params
  })
}

// export function getEquipmentList (params) {
//   return request({
//     url: `/admin/equipment/getList`,
//     method: 'get',
//     params
//   })
// }
export function getEquipmentList (params) {
  return request({
    url: `/admin/equipment/getTreeList`,
    method: 'get',
    params
  })
}
export function del (id) {
  return request({
    url: `/admin/resourcepack/delete/` + id,
    method: 'delete'
  })
}

export function send (data) {
  return request({
    url: `/admin/resourcepack/pub`,
    method: 'post',
    data
  })
}

export function sendByPackName (data) {
  return request({
    url: `/admin/resourcepack/pubByPackName`,
    method: 'post',
    data
  })
}
export function getGroupList (params) {
  return request({
    url: `/admin/equipment/getGroupList`,
    method: 'get',
    params
  })
}


