import request, { postBlob, getBlob, handleImport } from '@/utils/request'

export function getList (params) {
  return request({
    url: `/admin/equipment/getList`,
    method: 'get',
    params
  })
}

export function getTreeList (params) {
  return request({
    url: `/admin/equipment/getTreeList`,
    method: 'get',
    params
  })
}

export function add (data) {
  return request({
    url: `/manage/equipment/addEquipment`,
    method: 'post',
    data
  })
}

export function edit (data) {
  return request({
    url: `/admin/equipment/editEquipment`,
    method: 'put',
    data
  })
}

export function del (id) {
  return request({
    url: `/admin/equipment/delete/` + id,
    method: 'delete'
  })
}

export function getGroupNameList (params) {
  return request({
    url: `/admin/equipment/groupNameList`,
    method: 'get',
    params
  })
}

// 分组管理相关API
export function addGroup (data) {
  return request({
    url: `/admin/equipment/addGroup`,
    method: 'post',
    data
  })
}

export function editGroup (data) {
  return request({
    url: `/admin/equipment/editGroup`,
    method: 'put',
    data
  })
}

export function deleteGroup (id) {
  return request({
    url: `/admin/equipment/deleteGroup/${id}`,
    method: 'delete'
  })
}

export function getGroupDetail (id) {
  return request({
    url: `/admin/equipment/getGroupDetail/${id}`,
    method: 'get'
  })
}

// 批量上传文件并关联设备
export function batchUploadFiles (data) {
  return request({
    url: `/admin/sourcematerial/batchUpload`,
    method: 'post',
    data, // data 应该是 FormData
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 发送指令
export function sendCommand (data) {
  return request({
    url: `/admin/equipment/sendCommand`,
    method: 'post',
    data
  })
}

export function getMaterial (id) {
  return request({
    url: `/admin/equipment/getMaterial/` + id,
    method: 'get'
  })
}

export function pushMaterial (data) {
  return request({
    url: `/admin/equipment/pushMaterial`,
    method: 'post',
    data
  })
}


export function associateMaterial (data) {
  return request({
    url: `/admin/equipment/associateMaterial`,
    method: 'post',
    data
  })
}

export function disassociateMaterial (data) {
  return request({
    url: `/admin/equipment/disassociateMaterial`,
    method: 'post',
    data
  })
}

export function batchPushMaterial (data) {
  return request({
    url: `/admin/equipment/batchPushMaterial`,
    method: 'post',
    data
  })
}

export function getLatestScreenshot (groupName, equipmentAliasName) {
  return request({
    url: `/admin/equipment/getLatestScreenshot`,
    method: 'get',
    params: {
      group_name: groupName,
      equipment_alias_name: equipmentAliasName
    }
  })
}

export function getScreenshotByOperationId (operationId) {
  return request({
    url: `/admin/equipment/getScreenshotByOperationId`,
    method: 'get',
    params: {
      operation_id: operationId
    }
  })
}

export function getScreenshotByTaskId (taskId) {
  return request({
    url: `/admin/equipment/getScreenshotByTaskId`,
    method: 'get',
    params: {
      task_id: taskId
    }
  })
}

export function deleteScreenshot (data) {
  return request({
    url: `/admin/equipment/screenshot`,
    method: 'delete',
    data
  })
}

export function getEquipmentLog (params) {
  return request({
    url: `/admin/equipment/log`,
    method: 'get',
    params
  })
}

export function clearEquipmentLog (data) {
  return request({
    url: `/admin/equipment/log`,
    method: 'delete',
    data
  })
}

// 保存定时开关机设置
export function saveEquipmentPowerSchedules (data) {
  return request({
    url: `/admin/equipment/schedules`,
    method: 'post',
    data
  })
}

// 获取定时开关机设置
export function getEquipmentPowerSchedules (equipmentId) {
  return request({
    url: `/admin/equipment/schedules/${equipmentId}`,
    method: 'get'
  })
}

// 上传文件到指定设备
export function uploadEquipmentMaterial (data, config) {
  return request({
    url: 'admin/sourcematerial/upload/equipment',
    method: 'post',
    data: data,
    ...config
  })
}

// 获取最新的设备上报信息
export function getLatestDeviceReport (data) {
  return request({
    url: `/admin/equipment/latest_report`,
    method: 'post',
    data
  })
}

// 批量删除文件
export function batchDeleteFiles (data) {
  return request({
    url: `/admin/equipment/batchDeleteFiles`,
    method: 'post',
    data
  })
}

// 获取设备当前打开文件
export function getCurrentOpenFiles (data) {
  return request({
    url: `/admin/equipment/current_open_files`,
    method: 'post',
    data
  })
}
