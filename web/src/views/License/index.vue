<template>
  <div class="license-page">
    <el-tabs v-model="activeTab" type="border-card" class="section">
      <el-tab-pane :label="$t('license.serverTab')" name="server"></el-tab-pane>
      <el-tab-pane :label="$t('license.terminalTab')" name="terminal"></el-tab-pane>
    </el-tabs>

    <div v-show="activeTab === 'server'">
      <el-card shadow="never" class="section">
        <div slot="header" class="clearfix">
          <span>{{ $t('license.serverLicense') }}</span>
          <el-tag v-if="serverStatus" size="mini" :type="serverStatus.valid ? 'success' : 'warning'"
            style="margin-left:8px;">
            {{ serverStatus.valid ? $t('license.valid') : $t('license.invalid') }}
          </el-tag>
        </div>
        <div class="row">
          <el-button type="primary" icon="el-icon-download" @click="onExportServerMachineCode"
            :loading="loading.serverExport">{{ $t('license.exportServerMachineCode') }}</el-button>
          <el-upload action="/" :show-file-list="false" :on-change="onImportServerLicense" :auto-upload="false"
            accept=".dat">
            <el-button type="success" icon="el-icon-upload2" :loading="loading.serverImport">{{
              $t('license.importServerLicense') }}</el-button>
          </el-upload>
          <el-alert v-if="serverStatus && serverStatus.expire_at" :closable="false" type="info" class="ml-12">
            {{ $t('license.expireTime') }}：{{ formatTs(serverStatus.expire_at) }}
          </el-alert>
        </div>
      </el-card>
    </div>

    <div v-show="activeTab === 'terminal'">
      <el-card shadow="never" class="section">
        <div slot="header" class="clearfix">
          <span>{{ $t('license.terminalLicense') }}</span>
        </div>
        <div class="row">
          <el-button @click="onExportAllTerminalCodes" icon="el-icon-download" :loading="loading.termExportAll">{{
            $t('license.exportTerminalCodes') }}</el-button>
          <el-button @click="onExportUnregisteredTerminalCodes" icon="el-icon-download"
            :loading="loading.termExportUnreg">{{ $t('license.exportUnregisteredTerminalCodes') }}</el-button>
          <el-upload action="/" :show-file-list="false" :on-change="onImportTerminalCodes" :auto-upload="false"
            accept=".dat">
            <el-button type="success" icon="el-icon-upload2" :loading="loading.termImport">{{
              $t('license.importTerminalCodes') }}</el-button>
          </el-upload>
        </div>

        <div class="mt-16">
          <el-input v-model="query.keyword" :placeholder="$t('license.searchDeviceAliasMac')" clearable
            style="width: 260px;" class="mr-8" />
          <el-button type="primary" @click="fetchTerminalStatus">{{ $t('public.search') }}</el-button>
        </div>
        <el-table :data="terminalList" border size="small" class="mt-12">
          <el-table-column prop="alias_name" :label="$t('license.deviceAlias')" width="200">
            <template slot-scope="scope">{{ decodeText(scope.row.alias_name) }}</template>
          </el-table-column>
          <el-table-column prop="mac_address" :label="$t('license.macAddress')" width="220">
            <template slot-scope="scope">{{ formatMac(scope.row.mac_address) }}</template>
          </el-table-column>
          <el-table-column prop="registered" :label="$t('license.registrationStatus')" width="120">
            <template slot-scope="scope">
              <el-tag :type="scope.row.registered ? 'success' : 'info'">{{ scope.row.registered ?
                $t('license.registered') : $t('license.unregistered')
                }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="registration_code" :label="$t('license.registrationCode')" min-width="200">
            <template slot-scope="scope">{{ decodeText(scope.row.registration_code).toUpperCase() || '-' }}</template>
          </el-table-column>
          <el-table-column prop="created_at" :label="$t('license.creationTime')" width="180">
            <template slot-scope="scope">{{ formatTs(scope.row.created_at) }}</template>
          </el-table-column>
        </el-table>
        <div class="mt-12" style="text-align:right;">
          <el-pagination background layout="prev, pager, next, jumper" :total="total" :page-size="query.page_size"
            :current-page.sync="query.page" @current-change="fetchTerminalStatus" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import {
  exportServerMachineCode,
  importServerLicense,
  getServerLicenseStatus,
  exportAllTerminalMachineCodes,
  exportUnregisteredTerminalMachineCodes,
  importTerminalRegistrationCodes,
  getTerminalRegistrationStatus
} from '@/api/license'

export default {
  name: 'LicensePage',
  props: ['tab'],
  data () {
    return {
      activeTab: 'server',
      loading: {
        serverExport: false,
        serverImport: false,
        termExportAll: false,
        termExportUnreg: false,
        termImport: false,
      },
      serverStatus: null,
      query: { page: 1, page_size: 10, keyword: '' },
      terminalList: [],
      total: 0,
    }
  },
  watch: {
    tab: {
      immediate: true,
      handler (val) {
        if (val === 'equipment') {
          this.activeTab = 'terminal'
        } else if (val === 'server') {
          this.activeTab = 'server'
        }
      }
    },
    activeTab (newTab) {
      const newRouteParam = newTab === 'terminal' ? 'equipment' : 'server'
      if (this.$route.params.tab !== newRouteParam) {
        this.$router.replace({ name: 'License', params: { tab: newRouteParam } })
      }
    }
  },
  created () {
    this.fetchServerStatus()
    this.fetchTerminalStatus()
    this.updateActiveTabFromRoute()
  },
  methods: {
    updateActiveTabFromRoute () {
      const tab = this.$route.params.tab
      if (tab === 'equipment') {
        this.activeTab = 'terminal'
      } else if (tab === 'server') {
        this.activeTab = 'server'
      }
    },
    decodeText (v) {
      if (!v) return ''
      if (typeof v === 'string') return v
      try {
        // 尝试把像 "[97 118 109]" 这样的 Go 字节切片字符串转成文本
        const m = String(v).match(/^\[([0-9\s]+)\]$/)
        if (m && m[1]) {
          const codes = m[1].trim().split(/\s+/).map(n => parseInt(n, 10)).filter(n => !isNaN(n))
          return String.fromCharCode(...codes)
        }
        return String(v)
      } catch (e) {
        return String(v)
      }
    },
    formatMac (v) {
      const raw = this.decodeText(v).replace(/[^0-9A-Fa-f]/g, '')
      if (!raw) return ''
      const up = raw.toUpperCase()
      return up.match(/.{1,2}/g)?.join(':') || up
    },
    formatTs (v) {
      if (!v) return '-'
      const d = new Date(v * 1000)
      const pad = n => (n < 10 ? '0' + n : n)
      return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}`
    },
    async fetchServerStatus () {
      try {
        const res = await getServerLicenseStatus()
        this.serverStatus = res.data || null
      } catch (e) {
        // ignore if not implemented yet
      }
    },
    async onExportServerMachineCode () {
      this.loading.serverExport = true
      try {
        await exportServerMachineCode()
      } finally {
        this.loading.serverExport = false
      }
    },
    async onImportServerLicense (file) {
      const isDat = file.raw.type === 'application/octet-stream' || file.name.slice(-4) === '.dat'
      if (!isDat) {
        this.$message.error(this.$t('license.onlyDatFiles'))
        return
      }
      this.loading.serverImport = true
      try {

        const fd = new FormData()
        fd.append('file', file.raw)
        let ress = await importServerLicense(fd)
        if (ress.code == 0) {
          this.$message.success(this.$t('license.serverLicenseImported'))
          this.fetchServerStatus()

        } else {
          this.$message.error(this.$t('license.serverLicenseImportFailed'))
        }



      } catch (e) {
        // handled by interceptor
        console.log(e, "error");

      } finally {
        this.loading.serverImport = false
      }
    },
    async onExportAllTerminalCodes () {
      this.loading.termExportAll = true
      try {
        await exportAllTerminalMachineCodes()
      } finally {
        this.loading.termExportAll = false
      }
    },
    async onExportUnregisteredTerminalCodes () {
      this.loading.termExportUnreg = true
      try {
        await exportUnregisteredTerminalMachineCodes()
      } finally {
        this.loading.termExportUnreg = false
      }
    },
    async onImportTerminalCodes (file) {
      const isDat = file.raw.type === 'application/octet-stream' || file.name.slice(-4).toLowerCase() === '.dat'
      if (!isDat) {
        this.$message.error(this.$t('license.onlyDatFiles'))
        return
      }
      this.loading.termImport = true
      try {

        const fd = new FormData()
        fd.append('file', file.raw)
        let res = await importTerminalRegistrationCodes(fd)
        console.log(res, "resresresresresres")
        if (res.code == 0) {
          this.$message.success(this.$t('license.terminalCodesImported'))
          this.fetchTerminalStatus()
        } else {
          this.$message.error(this.$t('license.terminalCodesImportFailed'))

        }
      } catch (e) {
        // handled globally
        console.log(e)
      } finally {
        this.loading.termImport = false
      }
    },
    async fetchTerminalStatus () {
      try {
        const res = await getTerminalRegistrationStatus(this.query)
        this.terminalList = res.data?.list || []
        this.total = res.data?.total || 0
      } catch (e) {
        this.terminalList = []
        this.total = 0
      }
    }
  }
}
</script>

<style scoped>
.license-page {
  padding: 16px;
}

.section {
  margin-bottom: 16px;
}

.row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ml-12 {
  margin-left: 12px;
}

.mt-12 {
  margin-top: 12px;
}

.mt-16 {
  margin-top: 16px;
}

.mr-8 {
  margin-right: 8px;
}
</style>
